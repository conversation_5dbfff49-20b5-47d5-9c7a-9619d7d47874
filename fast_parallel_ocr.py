#!/usr/bin/env python3
"""
CPU-Optimized Parallel Multi-Method OCR Script
Uses aggressive multiprocessing and optimized preprocessing to maximize speed
"""

import cv2
import pytesseract
import numpy as np
from PIL import Image
import os
import json
from datetime import datetime
import hashlib
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor
import time
import psutil
from functools import partial

class FastParallelOCR:
    def __init__(self, image_path, reference_pem_path="rsa_private_key.pem", num_workers=None):
        self.image_path = image_path
        self.reference_pem_path = reference_pem_path
        self.reference_lines = self._load_reference_lines()
        self.valid_results = []
        self.all_attempts = []
        
        # Use maximum CPU power available
        if num_workers is None:
            self.num_workers = mp.cpu_count()  # Use all cores
        else:
            self.num_workers = num_workers
            
        print(f"⚡ Using {self.num_workers} parallel workers (ALL CPU cores: {mp.cpu_count()})")
        
        # Pre-load and cache image
        self.original_image = cv2.imread(self.image_path)
        if self.original_image is None:
            raise ValueError(f"Could not load image: {self.image_path}")
        
        # Pre-compute image data for serialization
        self.image_shape = self.original_image.shape
        self.image_data = self.original_image.tobytes()
    
    def _load_reference_lines(self):
        """Load the first 4 lines from the reference RSA private key file"""
        try:
            with open(self.reference_pem_path, 'r') as f:
                lines = [line.strip() for line in f.readlines()]
                if len(lines) >= 4:
                    reference_lines = lines[:4]
                    print(f"📋 Loaded reference lines from {self.reference_pem_path}")
                    return reference_lines
                else:
                    raise ValueError(f"Reference file {self.reference_pem_path} has less than 4 lines")
        except FileNotFoundError:
            print(f"❌ Reference file {self.reference_pem_path} not found, using fallback")
            return [
                "-----BEGIN RSA PRIVATE KEY-----",
                "MIIJKQIBAAKCAgEApXLc+tmBmDZl6NxuM+IohxYTOph3G/Pt/plQH3KsowSzM62E",
                "TSrkn9n3+qjLYU3ThgPPWCBBKdlf57c27eja37YWq7tlro6XD97YO4PtXMbiB6vn",
                "amPr13GFnuxQ1FtMDjGp91q234B2p7/QD4AJ/oz1TzAwyibAe80qWiuMqH3R6Kr0"
            ]
    
    def get_priority_methods(self):
        """Get the most effective preprocessing methods based on previous results"""
        # These are the most successful combinations from testing
        priority_methods = [
            # Scale variations with OTSU (highest success rate)
            {'scale': 2.0, 'blur': 0, 'threshold': 'otsu', 'morph': False},
            {'scale': 2.5, 'blur': 0, 'threshold': 'otsu', 'morph': False},
            {'scale': 1.5, 'blur': 0, 'threshold': 'otsu', 'morph': False},
            {'scale': 3.0, 'blur': 0, 'threshold': 'otsu', 'morph': False},
            
            # Adaptive threshold variations
            {'scale': 2.0, 'blur': 0, 'threshold': 'adaptive', 'morph': False},
            {'scale': 1.5, 'blur': 0, 'threshold': 'adaptive', 'morph': False},
            {'scale': 2.5, 'blur': 0, 'threshold': 'adaptive', 'morph': False},
            
            # Light blur combinations
            {'scale': 2.0, 'blur': 1, 'threshold': 'otsu', 'morph': False},
            {'scale': 2.5, 'blur': 1, 'threshold': 'otsu', 'morph': False},
            {'scale': 1.5, 'blur': 1, 'threshold': 'otsu', 'morph': False},
            
            # Morphological operations
            {'scale': 2.0, 'blur': 0, 'threshold': 'otsu', 'morph': True},
            {'scale': 1.5, 'blur': 0, 'threshold': 'otsu', 'morph': True},
            
            # Additional effective combinations
            {'scale': 1.0, 'blur': 0, 'threshold': 'otsu', 'morph': False},
            {'scale': 4.0, 'blur': 0, 'threshold': 'otsu', 'morph': False},
            {'scale': 2.0, 'blur': 3, 'threshold': 'otsu', 'morph': False},
        ]
        
        return priority_methods
    
    def get_priority_tesseract_configs(self):
        """Get the most effective Tesseract configurations"""
        # These are ordered by effectiveness from previous testing
        priority_configs = [
            # Most effective combinations first
            '--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz+/=- ',
            '--oem 3 --psm 4 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz+/=- ',
            '--oem 3 --psm 7 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz+/=- ',
            '--oem 1 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz+/=- ',
            '--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz+/=- ',
            
            # No whitelist variants (sometimes more accurate)
            '--oem 3 --psm 6',
            '--oem 3 --psm 4',
            '--oem 1 --psm 6',
            '--oem 3 --psm 7',
            '--oem 3 --psm 8',
            
            # Additional configurations
            '--oem 3 --psm 3 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz+/=- ',
            '--oem 1 --psm 4 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz+/=- ',
        ]
        
        return priority_configs

# Global function for multiprocessing (needs to be at module level)
def process_ocr_combination(args):
    """Process a single OCR combination - MUST be global for multiprocessing"""
    method_params, tesseract_config, image_data, image_shape, reference_lines = args
    
    try:
        # Reconstruct image from serialized data
        image = np.frombuffer(image_data, dtype=np.uint8).reshape(image_shape)
        
        # Fast preprocessing
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        
        # Apply scaling
        if method_params['scale'] != 1.0:
            height, width = gray.shape
            new_width = int(width * method_params['scale'])
            new_height = int(height * method_params['scale'])
            gray = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
        
        # Apply blur
        if method_params['blur'] > 0:
            gray = cv2.GaussianBlur(gray, (method_params['blur'], method_params['blur']), 0)
        
        # Apply thresholding
        if method_params['threshold'] == 'otsu':
            _, processed = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        elif method_params['threshold'] == 'adaptive':
            processed = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
        else:
            processed = gray
        
        # Apply morphological operations
        if method_params.get('morph', False):
            kernel = np.ones((2,2), np.uint8)
            processed = cv2.morphologyEx(processed, cv2.MORPH_CLOSE, kernel)
        
        # Run OCR
        text = pytesseract.image_to_string(processed, config=tesseract_config)
        
        # Extract lines
        lines = []
        for line in text.split('\n'):
            cleaned = line.strip()
            if cleaned and len(cleaned) > 5:
                lines.append(cleaned)
        
        # Validate first 4 lines
        is_valid = False
        if len(lines) >= 4:
            is_valid = all(lines[i] == reference_lines[i] for i in range(4))
        
        # Calculate similarity
        similarity = 0.0
        if len(lines) >= 4:
            total_score = 0.0
            for i in range(4):
                ref_line = reference_lines[i]
                ocr_line = lines[i]
                max_len = max(len(ref_line), len(ocr_line))
                if max_len > 0:
                    matches = sum(1 for a, b in zip(ref_line, ocr_line) if a == b)
                    score = matches / max_len
                    total_score += score
            similarity = total_score / 4.0
        
        # Create result
        result = {
            'method_params': method_params,
            'tesseract_config': tesseract_config,
            'lines': lines,
            'total_lines': len(lines),
            'is_valid': is_valid,
            'similarity': similarity,
            'method_hash': hashlib.md5(f"{method_params}{tesseract_config}".encode()).hexdigest()[:8]
        }
        
        return result
        
    except Exception as e:
        return {
            'method_params': method_params,
            'tesseract_config': tesseract_config,
            'error': str(e),
            'is_valid': False,
            'similarity': 0.0,
            'lines': []
        }

class FastParallelOCR:
    def __init__(self, image_path, reference_pem_path="rsa_private_key.pem", num_workers=None):
        self.image_path = image_path
        self.reference_pem_path = reference_pem_path
        self.reference_lines = self._load_reference_lines()
        self.valid_results = []
        self.all_attempts = []
        
        # Use maximum CPU power available
        if num_workers is None:
            self.num_workers = mp.cpu_count()
        else:
            self.num_workers = num_workers
            
        print(f"⚡ Using {self.num_workers} parallel workers (ALL CPU cores: {mp.cpu_count()})")
        
        # Pre-load and cache image
        self.original_image = cv2.imread(self.image_path)
        if self.original_image is None:
            raise ValueError(f"Could not load image: {self.image_path}")
        
        # Pre-compute image data for serialization
        self.image_shape = self.original_image.shape
        self.image_data = self.original_image.tobytes()
    
    def _load_reference_lines(self):
        """Load the first 4 lines from the reference RSA private key file"""
        try:
            with open(self.reference_pem_path, 'r') as f:
                lines = [line.strip() for line in f.readlines()]
                if len(lines) >= 4:
                    reference_lines = lines[:4]
                    print(f"📋 Loaded reference lines from {self.reference_pem_path}")
                    return reference_lines
                else:
                    raise ValueError(f"Reference file {self.reference_pem_path} has less than 4 lines")
        except FileNotFoundError:
            print(f"❌ Reference file {self.reference_pem_path} not found, using fallback")
            return [
                "-----BEGIN RSA PRIVATE KEY-----",
                "MIIJKQIBAAKCAgEApXLc+tmBmDZl6NxuM+IohxYTOph3G/Pt/plQH3KsowSzM62E",
                "TSrkn9n3+qjLYU3ThgPPWCBBKdlf57c27eja37YWq7tlro6XD97YO4PtXMbiB6vn",
                "amPr13GFnuxQ1FtMDjGp91q234B2p7/QD4AJ/oz1TzAwyibAe80qWiuMqH3R6Kr0"
            ]
    
    def get_priority_methods(self):
        """Get the most effective preprocessing methods"""
        return [
            {'scale': 2.0, 'blur': 0, 'threshold': 'otsu', 'morph': False},
            {'scale': 2.5, 'blur': 0, 'threshold': 'otsu', 'morph': False},
            {'scale': 1.5, 'blur': 0, 'threshold': 'otsu', 'morph': False},
            {'scale': 3.0, 'blur': 0, 'threshold': 'otsu', 'morph': False},
            {'scale': 2.0, 'blur': 0, 'threshold': 'adaptive', 'morph': False},
            {'scale': 1.5, 'blur': 0, 'threshold': 'adaptive', 'morph': False},
            {'scale': 2.5, 'blur': 0, 'threshold': 'adaptive', 'morph': False},
            {'scale': 2.0, 'blur': 1, 'threshold': 'otsu', 'morph': False},
            {'scale': 2.5, 'blur': 1, 'threshold': 'otsu', 'morph': False},
            {'scale': 2.0, 'blur': 0, 'threshold': 'otsu', 'morph': True},
            {'scale': 1.5, 'blur': 0, 'threshold': 'otsu', 'morph': True},
            {'scale': 1.0, 'blur': 0, 'threshold': 'otsu', 'morph': False},
            {'scale': 4.0, 'blur': 0, 'threshold': 'otsu', 'morph': False},
            {'scale': 2.0, 'blur': 3, 'threshold': 'otsu', 'morph': False},
        ]
    
    def get_priority_tesseract_configs(self):
        """Get the most effective Tesseract configurations"""
        return [
            '--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz+/=- ',
            '--oem 3 --psm 4 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz+/=- ',
            '--oem 3 --psm 7 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz+/=- ',
            '--oem 1 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz+/=- ',
            '--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz+/=- ',
            '--oem 3 --psm 6',
            '--oem 3 --psm 4',
            '--oem 1 --psm 6',
            '--oem 3 --psm 7',
            '--oem 3 --psm 8',
            '--oem 3 --psm 3 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz+/=- ',
            '--oem 1 --psm 4 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz+/=- ',
        ]
    
    def run_super_fast_ocr(self):
        """Run OCR using maximum CPU parallelization"""
        print("🚀 Starting SUPER FAST CPU-Parallel Multi-Method OCR")
        print(f"📸 Target image: {self.image_path}")
        print("🎯 Reference first 4 lines:")
        for i, line in enumerate(self.reference_lines, 1):
            print(f"  {i}: {line}")
        
        # Get optimized methods and configurations
        preprocessing_methods = self.get_priority_methods()
        tesseract_configs = self.get_priority_tesseract_configs()
        
        # Create all combinations
        combinations = []
        for method in preprocessing_methods:
            for config in tesseract_configs:
                combinations.append((method, config, self.image_data, self.image_shape, self.reference_lines))
        
        total_combinations = len(combinations)
        print(f"\n🔄 Processing {total_combinations} combinations with MAXIMUM CPU POWER:")
        print(f"  - {len(preprocessing_methods)} preprocessing methods")
        print(f"  - {len(tesseract_configs)} Tesseract configurations")
        print(f"  - Using ALL {self.num_workers} CPU cores")
        
        # Run with maximum parallelization
        start_time = time.time()
        valid_count = 0
        high_similarity_count = 0
        
        print(f"⚡ Starting MAXIMUM PARALLEL processing...")
        
        # Use all available CPU cores
        with ProcessPoolExecutor(max_workers=self.num_workers) as executor:
            # Submit all jobs at once for maximum parallelization
            futures = [executor.submit(process_ocr_combination, combo) for combo in combinations]
            
            # Process results as they complete
            for i, future in enumerate(futures):
                try:
                    result = future.result(timeout=60)  # Generous timeout
                    
                    if result:
                        self.all_attempts.append(result)
                        
                        if result['is_valid']:
                            valid_count += 1
                            self.valid_results.append(result)
                            print(f"✅ VALID RESULT #{valid_count} FOUND! (Progress: {i+1}/{total_combinations})")
                            print(f"   Hash: {result.get('method_hash', 'N/A')}, Lines: {result['total_lines']}")
                            
                            # If we find results quickly, show some excitement!
                            if valid_count == 1:
                                print(f"🎉 FIRST SUCCESS! Found a working method!")
                        
                        elif result.get('similarity', 0) > 0.85:
                            high_similarity_count += 1
                            if high_similarity_count <= 5:
                                print(f"🔍 High similarity result ({result['similarity']:.3f}): Progress {i+1}/{total_combinations}")
                        
                        # Rapid progress updates
                        if (i + 1) % 25 == 0 or valid_count > 0:
                            elapsed = time.time() - start_time
                            rate = (i + 1) / elapsed
                            eta = (total_combinations - i - 1) / rate if rate > 0 else 0
                            print(f"🚀 BLAZING FAST: {i+1}/{total_combinations} ({(i+1)/total_combinations*100:.1f}%) - Rate: {rate:.1f}/s - ETA: {eta:.1f}s")
                
                except Exception as e:
                    print(f"❌ Error processing combination {i}: {e}")
        
        elapsed_time = time.time() - start_time
        
        print(f"\n🏁 SUPER FAST Analysis Complete!")
        print(f"  ⚡ Total time: {elapsed_time:.2f} seconds")
        print(f"  🔥 Combinations/second: {total_combinations/elapsed_time:.2f}")
        print(f"  ✅ Valid results: {valid_count}")
        print(f"  🔍 High similarity (>85%): {high_similarity_count}")
        print(f"  📈 Success rate: {valid_count/total_combinations*100:.2f}%")
        
        if valid_count > 0:
            print(f"  🎯 Time to first success: {elapsed_time/total_combinations*min([i for i, r in enumerate(self.all_attempts) if r.get('is_valid', False)], default=total_combinations):.2f}s")
        
        return self.valid_results
    
    def save_results(self):
        """Save all results to files"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if self.valid_results:
            print(f"\n💾 Saving {len(self.valid_results)} VALID results...")
            
            for i, result in enumerate(self.valid_results, 1):
                filename = f"fast_ocr_valid_{i}_{result.get('method_hash', 'unknown')}_{timestamp}.txt"
                with open(filename, 'w') as f:
                    f.write(f"FAST OCR Valid Result #{i}\n")
                    f.write("=" * 50 + "\n\n")
                    f.write(f"Method Hash: {result.get('method_hash', 'N/A')}\n")
                    f.write(f"Similarity Score: {result['similarity']:.4f}\n")
                    f.write(f"Total Lines: {result['total_lines']}\n\n")
                    f.write("Method Parameters:\n")
                    f.write(f"{json.dumps(result['method_params'], indent=2)}\n\n")
                    f.write("Tesseract Config:\n")
                    f.write(f"{result['tesseract_config']}\n\n")
                    f.write("Extracted RSA Private Key:\n")
                    f.write("-" * 30 + "\n")
                    for line in result['lines']:
                        f.write(f"{line}\n")
                
                print(f"  ✅ Saved: {filename}")
        
        # Save analysis summary
        analysis_filename = f"fast_ocr_analysis_{timestamp}.json"
        with open(analysis_filename, 'w') as f:
            json.dump({
                'timestamp': timestamp,
                'image_path': self.image_path,
                'total_attempts': len(self.all_attempts),
                'valid_results': len(self.valid_results),
                'num_workers': self.num_workers,
                'cpu_cores': mp.cpu_count(),
                'best_similarity_scores': sorted([r.get('similarity', 0) for r in self.all_attempts], reverse=True)[:10],
                'reference_lines': self.reference_lines
            }, f, indent=2)
        
        print(f"  📈 Analysis saved: {analysis_filename}")

def main():
    image_path = "WhatsApp Image 2025-06-06 at 09.57.47.jpeg"
    
    if not os.path.exists(image_path):
        print(f"❌ Image file not found: {image_path}")
        return
    
    # Show system capabilities
    print(f"💻 System Ready for MAXIMUM SPEED:")
    print(f"   CPU cores: {mp.cpu_count()}")
    print(f"   Available RAM: {psutil.virtual_memory().available / (1024**3):.1f} GB")
    
    try:
        # Use ALL available CPU cores for maximum speed
        ocr = FastParallelOCR(image_path, num_workers=mp.cpu_count())
        
        valid_results = ocr.run_super_fast_ocr()
        ocr.save_results()
        
        if valid_results:
            print(f"\n🎉 HUGE SUCCESS! Found {len(valid_results)} valid OCR results!")
            print("\n🏆 Best Results:")
            for i, result in enumerate(sorted(valid_results, key=lambda x: x['similarity'], reverse=True)[:3], 1):
                print(f"  #{i}: Similarity {result['similarity']:.4f}, {result['total_lines']} lines")
                print(f"      Hash: {result.get('method_hash', 'N/A')}")
        else:
            print("\n😞 No perfect matches found, but check high similarity results.")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
