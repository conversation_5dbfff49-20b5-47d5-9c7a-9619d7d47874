#!/usr/bin/env python3
"""
Example usage of the MultithreadedOCRValidator class
"""

from multithreaded_ocr_validator import MultithreadedOCRValidator
from pathlib import Path

def example_basic_usage():
    """Basic usage example"""
    print("=== Basic Usage Example ===")
    
    # Initialize the validator
    validator = MultithreadedOCRValidator(
        image_folder=".",  # Current directory
        reference_file="rsa_private_key.pem",
        max_workers=4  # Use 4 threads
    )
    
    # Run OCR processing
    summary = validator.run_multithreaded_ocr()
    
    # Print summary
    validator.print_summary(summary)
    
    # Save results
    json_file, txt_file = validator.save_results(summary, "example_results")
    print(f"Results saved to {json_file} and {txt_file}")

def example_custom_configuration():
    """Example with custom configuration"""
    print("\n=== Custom Configuration Example ===")
    
    # Initialize with custom settings
    validator = MultithreadedOCRValidator(
        image_folder="./images",  # Custom image folder
        reference_file="my_reference.txt",  # Custom reference file
        max_workers=2  # Limit to 2 threads
    )
    
    # Check if we have images to process
    image_files = validator.find_image_files()
    if not image_files:
        print("No image files found in ./images directory")
        return
    
    # Run processing
    summary = validator.run_multithreaded_ocr()
    
    # Show detailed results for perfect matches
    perfect_results = [r for r in summary['results'] if r.get('is_perfect_match', False)]
    if perfect_results:
        print(f"\nFound {len(perfect_results)} perfect matches:")
        for result in perfect_results:
            print(f"  - {Path(result['image_path']).name} with {result['config_id']}")
            print(f"    Processing time: {result['processing_time']:.3f}s")

def example_analyze_single_image():
    """Example of processing a single specific image"""
    print("\n=== Single Image Analysis Example ===")
    
    image_path = Path("WhatsApp Image 2025-06-06 at 09.57.47.jpeg")
    
    if not image_path.exists():
        print(f"Image {image_path} not found")
        return
    
    # Initialize validator
    validator = MultithreadedOCRValidator()
    
    # Process just this one image
    results = validator.process_single_image(image_path)
    
    # Find the best result
    best_result = max(results, key=lambda x: x.get('similarity_average', 0))
    
    print(f"Best OCR result for {image_path.name}:")
    print(f"  Configuration: {best_result['config_id']}")
    print(f"  Similarity: {best_result.get('similarity_average', 0):.3f}")
    print(f"  Perfect match: {best_result.get('is_perfect_match', False)}")
    print(f"  Processing time: {best_result['processing_time']:.3f}s")
    
    # Show extracted lines
    extracted_lines = best_result.get('extracted_lines', [])
    print(f"  Extracted {len(extracted_lines)} lines:")
    for i, line in enumerate(extracted_lines[:4], 1):
        print(f"    {i}: {line}")

def example_filter_results():
    """Example of filtering and analyzing results"""
    print("\n=== Results Filtering Example ===")
    
    validator = MultithreadedOCRValidator()
    summary = validator.run_multithreaded_ocr()
    
    # Filter results by similarity threshold
    high_quality_results = [
        r for r in summary['results'] 
        if r.get('similarity_average', 0) > 0.9
    ]
    
    print(f"Found {len(high_quality_results)} high-quality results (>90% similarity)")
    
    # Group by configuration
    config_performance = {}
    for result in summary['results']:
        config_id = result['config_id']
        similarity = result.get('similarity_average', 0)
        
        if config_id not in config_performance:
            config_performance[config_id] = []
        config_performance[config_id].append(similarity)
    
    # Show best performing configurations
    print("\nBest performing configurations:")
    for config_id, similarities in config_performance.items():
        avg_similarity = sum(similarities) / len(similarities)
        max_similarity = max(similarities)
        print(f"  {config_id}: avg={avg_similarity:.3f}, max={max_similarity:.3f}")

if __name__ == "__main__":
    # Run examples
    try:
        example_basic_usage()
        example_analyze_single_image()
        example_filter_results()
        
        # Only run custom config example if directories exist
        if Path("./images").exists():
            example_custom_configuration()
        else:
            print("\nSkipping custom configuration example (./images directory not found)")
            
    except Exception as e:
        print(f"Error running examples: {e}")
        import traceback
        traceback.print_exc()
