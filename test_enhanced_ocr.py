#!/usr/bin/env python3
"""
Quick test script for Enhanced Multithreaded OCR Validator
Demonstrates the new features with limited configuration testing
"""

from multithreaded_ocr_validator import EnhancedMultithreadedOCRValidator
import time

def test_enhanced_features():
    """Test the enhanced OCR validator with a subset of configurations"""
    print("🧪 Testing Enhanced OCR Validator Features")
    print("=" * 60)
    
    # Initialize with limited configurations for testing
    validator = EnhancedMultithreadedOCRValidator(
        image_folder=".",
        reference_file="rsa_private_key.pem",
        max_workers=4,  # Limit for testing
        use_gpu=True
    )
    
    # Override with smaller config set for testing
    test_configs = []
    
    # Priority configurations (known to work well)
    priority_configs = [
        {
            'preprocessing': {'name': 'otsu_thresh', 'scale': 3.0, 'threshold': 'otsu', 'denoise': False},
            'tesseract': {'psm': 6, 'oem': 3, 'whitelist': None},
            'priority': 1,
            'config_id': 'test_priority_3x_otsu_psm6'
        },
        {
            'preprocessing': {'name': 'fastNl_denoise', 'scale': 2.5, 'threshold': 'otsu', 'denoise': 'fastNl'},
            'tesseract': {'psm': 6, 'oem': 1, 'whitelist': 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=-'},
            'priority': 1,
            'config_id': 'test_priority_2.5x_fastNl_psm6'
        },
        {
            'preprocessing': {'name': 'advanced_combo1', 'scale': 2.0, 'threshold': 'otsu', 'denoise': 'fastNl', 'contrast': 1.3, 'sharpen': 'unsharp'},
            'tesseract': {'psm': 7, 'oem': 3, 'whitelist': 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=-'},
            'priority': 1,
            'config_id': 'test_advanced_combo_2x_psm7'
        }
    ]
    
    # Add some standard configurations
    standard_configs = [
        {
            'preprocessing': {'name': 'otsu_thresh', 'scale': 2.0, 'threshold': 'otsu', 'denoise': False},
            'tesseract': {'psm': 8, 'oem': 3, 'whitelist': None},
            'priority': 2,
            'config_id': 'test_standard_2x_otsu_psm8'
        },
        {
            'preprocessing': {'name': 'bilateral_denoise', 'scale': 1.5, 'threshold': 'adaptive', 'denoise': 'bilateral'},
            'tesseract': {'psm': 11, 'oem': 1, 'whitelist': 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=-'},
            'priority': 2,
            'config_id': 'test_standard_1.5x_bilateral_psm11'
        }
    ]
    
    test_configs.extend(priority_configs)
    test_configs.extend(standard_configs)
    
    # Override the full configuration set for testing
    validator.ocr_configs = test_configs
    
    print(f"🔧 Testing with {len(test_configs)} configurations")
    print(f"🎯 High confidence threshold: 95%")
    print(f"💻 Using {validator.max_workers} CPU cores")
    print()
    
    # Run the enhanced OCR processing
    start_time = time.time()
    summary = validator.run_enhanced_multithreaded_ocr()
    end_time = time.time()
    
    # Display results
    print("\n" + "=" * 60)
    print("🎯 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    print(f"⏱️  Processing time: {end_time - start_time:.2f} seconds")
    print(f"⚙️  Configurations tested: {summary['processed_configurations']}")
    print(f"⏭️  Configurations skipped: {summary['skipped_configurations']}")
    print(f"🎯 High confidence matches: {summary['high_confidence_matches']}")
    print(f"⭐ Excellent matches (≥90%): {summary['excellent_matches']}")
    print(f"🏆 Best similarity: {summary['best_similarity']:.3f}")
    print(f"⚡ Processing speed: {summary['configs_per_second']:.1f} configs/sec")
    
    # Show best result details
    if summary.get('best_overall_result'):
        best = summary['best_overall_result']
        print(f"\n🥇 BEST CONFIGURATION:")
        print(f"   ID: {best['config_id']}")
        print(f"   First 4 lines accuracy: {best.get('first_four_average', 0):.3f}")
        print(f"   Meets 95% threshold: {'✅ YES' if best.get('first_four_average', 0) >= 0.95 else '❌ NO'}")
        print(f"   Processing time: {best['processing_time']:.3f}s")
        
        # Show line-by-line results
        line_sims = best.get('line_similarities', [])
        if line_sims:
            print(f"   Line-by-line accuracy:")
            for i, sim in enumerate(line_sims[:4], 1):
                status = "✅" if sim >= 0.95 else "⚠️" if sim >= 0.8 else "❌"
                print(f"     Line {i}: {sim:.3f} {status}")
    
    # Test early termination feature
    print(f"\n🚀 EARLY TERMINATION TEST:")
    print(f"   Triggered: {'✅ YES' if summary['early_termination_triggered'] else '❌ NO'}")
    if summary['early_termination_triggered']:
        print(f"   Time saved: {summary['time_saved_seconds']:.1f} seconds")
    
    print("\n" + "=" * 60)
    print("✅ Enhanced OCR Validator test completed!")
    
    return summary

if __name__ == "__main__":
    try:
        test_enhanced_features()
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
