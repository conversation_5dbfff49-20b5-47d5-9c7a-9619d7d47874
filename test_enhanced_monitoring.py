#!/usr/bin/env python3
"""
Test script for Enhanced OCR Validator with Real-time Monitoring and GPU Acceleration
Demonstrates the new monitoring, multithreading verification, and GPU features
"""

from multithreaded_ocr_validator import EnhancedMultithreadedOCRValidator, SystemProfiler
import time
import threading

def test_system_detection():
    """Test system information detection"""
    print("🔍 Testing System Detection...")
    print("=" * 60)
    
    # Get and display system information
    system_info = SystemProfiler.get_system_info()
    SystemProfiler.display_system_info(system_info)
    
    # Test GPU detection specifically
    print(f"\n🎮 GPU Detection Results:")
    print(f"   Model: {system_info.gpu_model}")
    print(f"   VRAM: {system_info.gpu_vram_gb:.1f}GB")
    print(f"   CUDA Available: {system_info.cuda_available}")
    print(f"   CUDA Version: {system_info.cuda_version}")
    print(f"   OpenCV CUDA Support: {system_info.opencv_cuda_support}")
    print(f"   OpenCV OpenCL Support: {system_info.opencv_opencl_support}")

def test_multithreading_verification():
    """Test multithreading with limited configurations"""
    print("\n🧵 Testing Multithreading Verification...")
    print("=" * 60)
    
    # Test with ThreadPoolExecutor
    print("Testing ThreadPoolExecutor...")
    validator_threads = EnhancedMultithreadedOCRValidator(
        image_folder=".",
        reference_file="rsa_private_key.pem",
        max_workers=4,
        use_gpu=True,
        use_processes=False
    )
    
    # Override with minimal configs for testing
    test_configs = [
        {
            'preprocessing': {'name': 'otsu_thresh', 'scale': 2.0, 'threshold': 'otsu', 'denoise': False},
            'tesseract': {'psm': 6, 'oem': 3, 'whitelist': None},
            'priority': 1,
            'config_id': 'test_thread_2x_otsu'
        },
        {
            'preprocessing': {'name': 'fastNl_denoise', 'scale': 1.5, 'threshold': 'otsu', 'denoise': 'fastNl'},
            'tesseract': {'psm': 7, 'oem': 1, 'whitelist': 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=-'},
            'priority': 1,
            'config_id': 'test_thread_1.5x_fastNl'
        }
    ]
    
    validator_threads.ocr_configs = test_configs
    
    print(f"🔧 Testing with {len(test_configs)} configurations using ThreadPoolExecutor")
    start_time = time.time()
    summary_threads = validator_threads.run_enhanced_multithreaded_ocr()
    thread_time = time.time() - start_time
    
    print(f"\n📊 ThreadPoolExecutor Results:")
    print(f"   Time: {thread_time:.2f}s")
    print(f"   Processed: {summary_threads['processed_configurations']}")
    print(f"   Best similarity: {summary_threads['best_similarity']:.3f}")
    
    # Test with ProcessPoolExecutor
    print(f"\n🔄 Testing ProcessPoolExecutor...")
    validator_processes = EnhancedMultithreadedOCRValidator(
        image_folder=".",
        reference_file="rsa_private_key.pem",
        max_workers=4,
        use_gpu=True,
        use_processes=True
    )
    
    validator_processes.ocr_configs = test_configs
    
    start_time = time.time()
    summary_processes = validator_processes.run_enhanced_multithreaded_ocr()
    process_time = time.time() - start_time
    
    print(f"\n📊 ProcessPoolExecutor Results:")
    print(f"   Time: {process_time:.2f}s")
    print(f"   Processed: {summary_processes['processed_configurations']}")
    print(f"   Best similarity: {summary_processes['best_similarity']:.3f}")
    
    # Compare performance
    print(f"\n⚡ Performance Comparison:")
    if thread_time > 0 and process_time > 0:
        if thread_time < process_time:
            speedup = process_time / thread_time
            print(f"   ThreadPoolExecutor is {speedup:.1f}x faster")
        else:
            speedup = thread_time / process_time
            print(f"   ProcessPoolExecutor is {speedup:.1f}x faster")
    
    return summary_threads, summary_processes

def test_progress_monitoring():
    """Test real-time progress monitoring"""
    print("\n📊 Testing Progress Monitoring...")
    print("=" * 60)
    
    validator = EnhancedMultithreadedOCRValidator(
        image_folder=".",
        reference_file="rsa_private_key.pem",
        max_workers=6,
        use_gpu=True,
        use_processes=False
    )
    
    # Create a moderate set of configurations for monitoring test
    test_configs = []
    scales = [1.5, 2.0, 2.5, 3.0]
    methods = ['otsu_thresh', 'fastNl_denoise', 'bilateral_denoise']
    psm_modes = [6, 7, 8]
    
    for scale in scales:
        for method in methods:
            for psm in psm_modes:
                config = {
                    'preprocessing': {
                        'name': method, 
                        'scale': scale, 
                        'threshold': 'otsu', 
                        'denoise': 'fastNl' if 'fastNl' in method else False
                    },
                    'tesseract': {'psm': psm, 'oem': 3, 'whitelist': None},
                    'priority': 2,
                    'config_id': f'monitor_test_{method}_s{scale}_psm{psm}'
                }
                test_configs.append(config)
    
    validator.ocr_configs = test_configs
    
    print(f"🔧 Testing progress monitoring with {len(test_configs)} configurations")
    print(f"💻 Using {validator.max_workers} threads")
    print(f"📊 Progress updates every 12 seconds")
    print(f"🎯 Watch for real-time updates during processing...")
    
    start_time = time.time()
    summary = validator.run_enhanced_multithreaded_ocr()
    total_time = time.time() - start_time
    
    print(f"\n🏁 Progress Monitoring Test Complete!")
    print(f"⏱️ Total time: {total_time:.2f}s")
    print(f"📊 Configurations processed: {summary['processed_configurations']}")
    print(f"⏭️ Configurations skipped: {summary['skipped_configurations']}")
    print(f"⚡ Average rate: {summary['configs_per_second']:.1f} configs/sec")
    print(f"🎯 High confidence results: {summary['high_confidence_matches']}")
    print(f"🏆 Best similarity: {summary['best_similarity']:.3f}")
    
    return summary

def test_gpu_acceleration():
    """Test GPU acceleration features"""
    print("\n🎮 Testing GPU Acceleration...")
    print("=" * 60)
    
    validator = EnhancedMultithreadedOCRValidator(
        image_folder=".",
        reference_file="rsa_private_key.pem",
        max_workers=4,
        use_gpu=True,
        use_processes=False
    )
    
    # Test GPU-specific configurations
    gpu_configs = [
        {
            'preprocessing': {
                'name': 'gpu_fastNl', 
                'scale': 3.0, 
                'threshold': 'otsu', 
                'denoise': 'fastNl'  # This should use GPU if available
            },
            'tesseract': {'psm': 6, 'oem': 3, 'whitelist': None},
            'priority': 1,
            'config_id': 'gpu_test_fastNl_3x'
        },
        {
            'preprocessing': {
                'name': 'gpu_otsu', 
                'scale': 2.5, 
                'threshold': 'otsu',  # This should use GPU if available
                'denoise': False
            },
            'tesseract': {'psm': 7, 'oem': 1, 'whitelist': 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=-'},
            'priority': 1,
            'config_id': 'gpu_test_otsu_2.5x'
        }
    ]
    
    validator.ocr_configs = gpu_configs
    
    print(f"🔧 Testing GPU acceleration with {len(gpu_configs)} configurations")
    print(f"🎮 GPU Available: {validator.gpu_available}")
    print(f"🚀 GPU Model: {validator.system_info.gpu_model}")
    
    if validator.gpu_available:
        print(f"✅ GPU acceleration should be used for large image operations")
    else:
        print(f"⚠️ GPU not available, will use CPU fallback")
    
    start_time = time.time()
    summary = validator.run_enhanced_multithreaded_ocr()
    gpu_time = time.time() - start_time
    
    print(f"\n📊 GPU Test Results:")
    print(f"   Time: {gpu_time:.2f}s")
    print(f"   Processed: {summary['processed_configurations']}")
    print(f"   Best similarity: {summary['best_similarity']:.3f}")
    print(f"   GPU acceleration used: {summary['gpu_acceleration_used']}")
    
    return summary

def main():
    """Run all enhanced feature tests"""
    print("🚀 ENHANCED OCR VALIDATOR FEATURE TESTING")
    print("=" * 80)
    
    try:
        # Test 1: System Detection
        test_system_detection()
        
        # Test 2: Multithreading Verification
        thread_summary, process_summary = test_multithreading_verification()
        
        # Test 3: Progress Monitoring
        progress_summary = test_progress_monitoring()
        
        # Test 4: GPU Acceleration
        gpu_summary = test_gpu_acceleration()
        
        # Final Summary
        print(f"\n{'='*80}")
        print(f"🎉 ALL ENHANCED FEATURES TESTED SUCCESSFULLY!")
        print(f"{'='*80}")
        print(f"✅ System detection and information display")
        print(f"✅ Multithreading verification (Thread vs Process pools)")
        print(f"✅ Real-time progress monitoring")
        print(f"✅ GPU acceleration testing")
        print(f"✅ Enhanced performance metrics")
        print(f"{'='*80}")
        
    except KeyboardInterrupt:
        print("\n🛑 Testing interrupted by user")
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
