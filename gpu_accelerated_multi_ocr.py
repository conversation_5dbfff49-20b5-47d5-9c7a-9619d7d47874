#!/usr/bin/env python3
"""
GPU and CPU Accelerated Multi-Method OCR Script
Uses multiprocessing, GPU acceleration (when available), and optimized preprocessing
to run OCR methods much faster.
"""

import cv2
import pytesseract
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
import os
import json
from datetime import datetime
import hashlib
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
from functools import partial
import time
import psutil

# Try to import GPU acceleration libraries
try:
    import cupy as cp
    GPU_AVAILABLE = True
    print("🚀 GPU acceleration available (CuPy)")
except ImportError:
    try:
        import pyopencl as cl
        GPU_AVAILABLE = True
        print("🚀 GPU acceleration available (OpenCL)")
    except ImportError:
        GPU_AVAILABLE = False
        print("ℹ️  GPU acceleration not available, using CPU only")

class GPUAcceleratedOCR:
    def __init__(self, image_path, reference_pem_path="rsa_private_key.pem", num_workers=None):
        self.image_path = image_path
        self.reference_pem_path = reference_pem_path
        self.reference_lines = self._load_reference_lines()
        self.valid_results = []
        self.all_attempts = []
        
        # Set optimal number of workers based on CPU cores
        if num_workers is None:
            self.num_workers = min(mp.cpu_count(), 8)  # Cap at 8 to avoid resource exhaustion
        else:
            self.num_workers = num_workers
            
        print(f"🔧 Using {self.num_workers} parallel workers (CPU cores: {mp.cpu_count()})")
        
        # Pre-load image for all workers
        self.original_image = cv2.imread(self.image_path)
        if self.original_image is None:
            raise ValueError(f"Could not load image: {self.image_path}")
    
    def _load_reference_lines(self):
        """Load the first 4 lines from the reference RSA private key file"""
        try:
            with open(self.reference_pem_path, 'r') as f:
                lines = [line.strip() for line in f.readlines()]
                if len(lines) >= 4:
                    reference_lines = lines[:4]
                    print(f"📋 Loaded reference lines from {self.reference_pem_path}")
                    return reference_lines
                else:
                    raise ValueError(f"Reference file {self.reference_pem_path} has less than 4 lines")
        except FileNotFoundError:
            print(f"❌ Reference file {self.reference_pem_path} not found, using fallback")
            return [
                "-----BEGIN RSA PRIVATE KEY-----",
                "MIIJKQIBAAKCAgEApXLc+tmBmDZl6NxuM+IohxYTOph3G/Pt/plQH3KsowSzM62E",
                "TSrkn9n3+qjLYU3ThgPPWCBBKdlf57c27eja37YWq7tlro6XD97YO4PtXMbiB6vn",
                "amPr13GFnuxQ1FtMDjGp91q234B2p7/QD4AJ/oz1TzAwyibAe80qWiuMqH3R6Kr0"
            ]
    
    def preprocess_image_gpu(self, image, method_params):
        """GPU-accelerated preprocessing using CuPy (if available)"""
        if not GPU_AVAILABLE:
            return self.preprocess_image_cv2(image, method_params)
        
        try:
            # Convert to GPU array
            gpu_image = cp.asarray(image)
            
            if len(gpu_image.shape) == 3:
                # Convert to grayscale on GPU
                weights = cp.array([0.299, 0.587, 0.114])
                gray = cp.sum(gpu_image * weights, axis=2).astype(cp.uint8)
            else:
                gray = gpu_image.copy()
            
            # Apply scaling on GPU
            if method_params['scale'] != 1.0:
                height, width = gray.shape
                new_height = int(height * method_params['scale'])
                new_width = int(width * method_params['scale'])
                # Use GPU-based resize (simplified version)
                gray = cp.resize(gray, (new_height, new_width))
            
            # Convert back to CPU for OpenCV operations
            cpu_image = cp.asnumpy(gray).astype(np.uint8)
            
            # Apply remaining operations on CPU (more stable)
            if method_params['blur'] > 0:
                cpu_image = cv2.GaussianBlur(cpu_image, (method_params['blur'], method_params['blur']), 0)
            
            if method_params['threshold_method'] == 'otsu':
                _, processed = cv2.threshold(cpu_image, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            elif method_params['threshold_method'] == 'adaptive':
                processed = cv2.adaptiveThreshold(cpu_image, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
            else:
                processed = cpu_image
            
            return processed
            
        except Exception as e:
            # Fallback to CPU processing
            print(f"GPU processing failed, falling back to CPU: {e}")
            return self.preprocess_image_cv2(image, method_params)
    
    def preprocess_image_cv2(self, image, method_params):
        """Optimized OpenCV preprocessing methods"""
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        
        # Apply scaling
        if method_params['scale'] != 1.0:
            height, width = gray.shape
            new_width = int(width * method_params['scale'])
            new_height = int(height * method_params['scale'])
            gray = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
        
        # Apply blur
        if method_params['blur'] > 0:
            gray = cv2.GaussianBlur(gray, (method_params['blur'], method_params['blur']), 0)
        
        # Apply thresholding
        if method_params['threshold_method'] == 'otsu':
            _, processed = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        elif method_params['threshold_method'] == 'adaptive':
            processed = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
        elif method_params['threshold_method'] == 'manual':
            _, processed = cv2.threshold(gray, method_params['threshold_value'], 255, cv2.THRESH_BINARY)
        else:
            processed = gray
        
        # Apply morphological operations
        if method_params.get('morph', False):
            kernel = np.ones((2,2), np.uint8)
            processed = cv2.morphologyEx(processed, cv2.MORPH_CLOSE, kernel)
        
        return processed
    
    def get_fast_preprocessing_methods(self):
        """Generate optimized set of preprocessing methods for speed"""
        methods = []
        
        # Most effective combinations based on testing
        high_priority_methods = [
            # Scale + OTSU combinations (most reliable)
            {'type': 'cv2', 'scale': 2.0, 'blur': 0, 'threshold_method': 'otsu', 'threshold_value': 127, 'morph': False},
            {'type': 'cv2', 'scale': 2.5, 'blur': 0, 'threshold_method': 'otsu', 'threshold_value': 127, 'morph': False},
            {'type': 'cv2', 'scale': 1.5, 'blur': 0, 'threshold_method': 'otsu', 'threshold_value': 127, 'morph': False},
            {'type': 'cv2', 'scale': 3.0, 'blur': 0, 'threshold_method': 'otsu', 'threshold_value': 127, 'morph': False},
            
            # Adaptive threshold combinations
            {'type': 'cv2', 'scale': 2.0, 'blur': 0, 'threshold_method': 'adaptive', 'threshold_value': 127, 'morph': False},
            {'type': 'cv2', 'scale': 1.5, 'blur': 0, 'threshold_method': 'adaptive', 'threshold_value': 127, 'morph': False},
            
            # With light blur
            {'type': 'cv2', 'scale': 2.0, 'blur': 1, 'threshold_method': 'otsu', 'threshold_value': 127, 'morph': False},
            {'type': 'cv2', 'scale': 2.5, 'blur': 1, 'threshold_method': 'otsu', 'threshold_value': 127, 'morph': False},
            
            # With morphological operations
            {'type': 'cv2', 'scale': 2.0, 'blur': 0, 'threshold_method': 'otsu', 'threshold_value': 127, 'morph': True},
            {'type': 'cv2', 'scale': 1.5, 'blur': 0, 'threshold_method': 'otsu', 'threshold_value': 127, 'morph': True},
        ]
        
        return high_priority_methods
    
    def get_fast_tesseract_configs(self):
        """Generate optimized Tesseract configurations for speed"""
        configs = []
        
        # Most effective PSM and OEM combinations
        effective_combinations = [
            # High-priority combinations
            {'oem': 3, 'psm': 6, 'whitelist': True},
            {'oem': 3, 'psm': 4, 'whitelist': True},
            {'oem': 3, 'psm': 7, 'whitelist': True},
            {'oem': 1, 'psm': 6, 'whitelist': True},
            {'oem': 3, 'psm': 8, 'whitelist': True},
            
            # No whitelist variants
            {'oem': 3, 'psm': 6, 'whitelist': False},
            {'oem': 3, 'psm': 4, 'whitelist': False},
            {'oem': 1, 'psm': 6, 'whitelist': False},
        ]
        
        for combo in effective_combinations:
            if combo['whitelist']:
                config = f"--oem {combo['oem']} --psm {combo['psm']} -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz+/=- "
            else:
                config = f"--oem {combo['oem']} --psm {combo['psm']}"
            configs.append(config)
        
        return configs
    
    def extract_lines_from_text(self, text):
        """Extract and clean lines from OCR text"""
        lines = []
        for line in text.split('\n'):
            cleaned = line.strip()
            if cleaned and len(cleaned) > 5:
                lines.append(cleaned)
        return lines
    
    def validate_first_4_lines(self, lines):
        """Check if first 4 lines match reference"""
        if len(lines) < 4:
            return False
        
        for i in range(4):
            if lines[i] != self.reference_lines[i]:
                return False
        
        return True
    
    def calculate_similarity(self, lines):
        """Calculate similarity score for first 4 lines"""
        if len(lines) < 4:
            return 0.0
        
        total_score = 0.0
        for i in range(4):
            if i < len(lines):
                # Character-by-character comparison
                ref_line = self.reference_lines[i]
                ocr_line = lines[i]
                
                max_len = max(len(ref_line), len(ocr_line))
                if max_len == 0:
                    continue
                
                matches = sum(1 for a, b in zip(ref_line, ocr_line) if a == b)
                score = matches / max_len
                total_score += score
        
        return total_score / 4.0
    
    def process_single_combination(self, args):
        """Process a single preprocessing + tesseract combination"""
        prep_method, tess_config, image_data = args
        
        try:
            # Reconstruct image from data
            image = np.frombuffer(image_data, dtype=np.uint8).reshape(self.original_image.shape)
            
            # Preprocess image
            if GPU_AVAILABLE and prep_method['type'] == 'cv2':
                processed_image = self.preprocess_image_gpu(image, prep_method)
            else:
                processed_image = self.preprocess_image_cv2(image, prep_method)
            
            # Run OCR
            text = pytesseract.image_to_string(processed_image, config=tess_config)
            
            # Extract lines
            lines = self.extract_lines_from_text(text)
            
            # Calculate metrics
            is_valid = self.validate_first_4_lines(lines)
            similarity = self.calculate_similarity(lines)
            
            # Create result
            result = {
                'preprocessing': prep_method,
                'tesseract_config': tess_config,
                'lines': lines,
                'total_lines': len(lines),
                'is_valid': is_valid,
                'similarity': similarity,
                'method_hash': hashlib.md5(f"{prep_method}{tess_config}".encode()).hexdigest()[:8]
            }
            
            return result
            
        except Exception as e:
            return {
                'preprocessing': prep_method,
                'tesseract_config': tess_config,
                'error': str(e),
                'is_valid': False,
                'similarity': 0.0
            }
    
    def run_parallel_ocr(self):
        """Run OCR using parallel processing"""
        print("🚀 Starting GPU/CPU Accelerated Multi-Method OCR")
        print(f"📸 Target image: {self.image_path}")
        print("🎯 Reference first 4 lines:")
        for i, line in enumerate(self.reference_lines, 1):
            print(f"  {i}: {line}")
        
        # Get methods and configurations
        preprocessing_methods = self.get_fast_preprocessing_methods()
        tesseract_configs = self.get_fast_tesseract_configs()
        
        # Create all combinations
        combinations = []
        image_data = self.original_image.tobytes()  # Serialize image once
        
        for prep_method in preprocessing_methods:
            for tess_config in tesseract_configs:
                combinations.append((prep_method, tess_config, image_data))
        
        total_combinations = len(combinations)
        print(f"\n🔄 Processing {total_combinations} combinations in parallel:")
        print(f"  - {len(preprocessing_methods)} preprocessing methods")
        print(f"  - {len(tesseract_configs)} Tesseract configurations")
        print(f"  - Using {self.num_workers} parallel workers")
        
        # Process combinations in parallel
        start_time = time.time()
        valid_count = 0
        high_similarity_count = 0
        
        with ProcessPoolExecutor(max_workers=self.num_workers) as executor:
            print(f"⚡ Starting parallel processing...")
            
            # Submit all jobs
            futures = {executor.submit(self.process_single_combination, combo): i 
                      for i, combo in enumerate(combinations)}
            
            # Process results as they complete
            for i, future in enumerate(futures):
                try:
                    result = future.result(timeout=30)  # 30 second timeout per job
                    
                    if result:
                        self.all_attempts.append(result)
                        
                        if result['is_valid']:
                            valid_count += 1
                            self.valid_results.append(result)
                            print(f"✅ Valid result #{valid_count} found! (Progress: {i+1}/{total_combinations})")
                            print(f"   Hash: {result.get('method_hash', 'N/A')}, Lines: {result['total_lines']}")
                        elif result.get('similarity', 0) > 0.85:
                            high_similarity_count += 1
                            if high_similarity_count <= 3:  # Show first 3
                                print(f"🔍 High similarity result ({result['similarity']:.3f}): Progress {i+1}/{total_combinations}")
                        
                        # Progress update every 20 results
                        if (i + 1) % 20 == 0:
                            elapsed = time.time() - start_time
                            rate = (i + 1) / elapsed
                            eta = (total_combinations - i - 1) / rate if rate > 0 else 0
                            print(f"📊 Progress: {i+1}/{total_combinations} ({(i+1)/total_combinations*100:.1f}%) - ETA: {eta:.1f}s")
                
                except Exception as e:
                    print(f"❌ Error processing combination {i}: {e}")
        
        elapsed_time = time.time() - start_time
        
        print(f"\n📊 Parallel Analysis Complete!")
        print(f"  ⏱️  Total time: {elapsed_time:.2f} seconds")
        print(f"  🔄 Combinations/second: {total_combinations/elapsed_time:.2f}")
        print(f"  ✅ Valid results: {valid_count}")
        print(f"  🔍 High similarity (>85%): {high_similarity_count}")
        print(f"  📈 Success rate: {valid_count/total_combinations*100:.2f}%")
        
        return self.valid_results
    
    def save_results(self):
        """Save all results to files"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save valid results
        if self.valid_results:
            print(f"\n💾 Saving {len(self.valid_results)} valid results...")
            
            for i, result in enumerate(self.valid_results, 1):
                filename = f"gpu_ocr_valid_{i}_{result.get('method_hash', 'unknown')}_{timestamp}.txt"
                with open(filename, 'w') as f:
                    f.write(f"GPU OCR Valid Result #{i}\n")
                    f.write("=" * 50 + "\n\n")
                    f.write(f"Method Hash: {result.get('method_hash', 'N/A')}\n")
                    f.write(f"Similarity Score: {result['similarity']:.4f}\n")
                    f.write(f"Total Lines: {result['total_lines']}\n\n")
                    f.write("Preprocessing Method:\n")
                    f.write(f"{json.dumps(result['preprocessing'], indent=2)}\n\n")
                    f.write("Tesseract Config:\n")
                    f.write(f"{result['tesseract_config']}\n\n")
                    f.write("Extracted Lines:\n")
                    f.write("-" * 30 + "\n")
                    for j, line in enumerate(result['lines'], 1):
                        f.write(f"{line}\n")
                
                print(f"  ✅ Saved: {filename}")
        
        # Save high similarity results
        high_sim_results = [r for r in self.all_attempts if r.get('similarity', 0) > 0.85 and not r.get('is_valid', False)]
        if high_sim_results:
            print(f"\n💾 Saving {len(high_sim_results)} high similarity results...")
            
            for i, result in enumerate(high_sim_results[:5], 1):  # Save top 5
                filename = f"gpu_ocr_high_sim_{i}_{timestamp}.txt"
                with open(filename, 'w') as f:
                    f.write(f"GPU OCR High Similarity Result #{i}\n")
                    f.write("=" * 50 + "\n\n")
                    f.write(f"Similarity Score: {result['similarity']:.4f}\n")
                    f.write(f"Total Lines: {result['total_lines']}\n\n")
                    f.write("First 4 Lines Comparison:\n")
                    f.write("-" * 30 + "\n")
                    for j in range(4):
                        f.write(f"Line {j+1}:\n")
                        if j < len(result['lines']):
                            f.write(f"OCR: {result['lines'][j]}\n")
                        else:
                            f.write(f"OCR: [MISSING]\n")
                        f.write(f"REF: {self.reference_lines[j]}\n\n")
                
                print(f"  📊 Saved: {filename}")
        
        # Save analysis summary
        analysis_filename = f"gpu_ocr_analysis_{timestamp}.json"
        with open(analysis_filename, 'w') as f:
            json.dump({
                'timestamp': timestamp,
                'image_path': self.image_path,
                'total_attempts': len(self.all_attempts),
                'valid_results': len(self.valid_results),
                'high_similarity_results': len(high_sim_results),
                'gpu_available': GPU_AVAILABLE,
                'num_workers': self.num_workers,
                'cpu_cores': mp.cpu_count(),
                'best_similarity_scores': sorted([r.get('similarity', 0) for r in self.all_attempts], reverse=True)[:10],
                'reference_lines': self.reference_lines
            }, f, indent=2)
        
        print(f"  📈 Analysis saved: {analysis_filename}")

def main():
    image_path = "WhatsApp Image 2025-06-06 at 09.57.47.jpeg"
    
    if not os.path.exists(image_path):
        print(f"❌ Image file not found: {image_path}")
        return
    
    # Check system resources
    print(f"💻 System Info:")
    print(f"   CPU cores: {mp.cpu_count()}")
    print(f"   Available RAM: {psutil.virtual_memory().available / (1024**3):.1f} GB")
    print(f"   GPU available: {GPU_AVAILABLE}")
    
    try:
        # Use optimal number of workers based on system
        optimal_workers = min(mp.cpu_count(), 6)  # Cap at 6 for stability
        ocr = GPUAcceleratedOCR(image_path, num_workers=optimal_workers)
        
        valid_results = ocr.run_parallel_ocr()
        ocr.save_results()
        
        if valid_results:
            print(f"\n🎉 SUCCESS! Found {len(valid_results)} valid OCR results!")
            print("\n🏆 Best Results:")
            for i, result in enumerate(sorted(valid_results, key=lambda x: x['similarity'], reverse=True)[:3], 1):
                print(f"  #{i}: Similarity {result['similarity']:.4f}, {result['total_lines']} lines")
                print(f"      Hash: {result.get('method_hash', 'N/A')}")
        else:
            print("\n😞 No perfect matches found, but analysis files saved for review.")
            print("Check the high similarity results for near-matches.")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
