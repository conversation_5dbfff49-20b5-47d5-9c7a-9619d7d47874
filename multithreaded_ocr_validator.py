#!/usr/bin/env python3
"""
Enhanced Multithreaded OCR Validator Script
High-performance OCR processing with comprehensive configuration testing and 95% accuracy threshold
"""

import os
import cv2
import pytesseract
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter, ImageOps
import threading
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, ProcessPoolExecutor, as_completed
import time
from datetime import datetime, timedelta
import json
import hashlib
from typing import List, Dict, Tuple, Optional, Set
import difflib
from pathlib import Path
import logging
import multiprocessing as mp
import psutil
from itertools import product
import queue
import signal
import sys
import platform
import subprocess
import gc
from dataclasses import dataclass
from collections import deque

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('enhanced_ocr_validator.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Global configuration constants
HIGH_CONFIDENCE_THRESHOLD = 0.95  # 95% minimum similarity for first 4 lines
GROUND_TRUTH_ACCURACY = 0.98      # 98% reference accuracy assumption
EARLY_TERMINATION_THRESHOLD = 0.95  # Stop when this accuracy is achieved

@dataclass
class SystemInfo:
    """System information and capabilities"""
    cpu_count: int
    cpu_model: str
    total_ram_gb: float
    available_ram_gb: float
    gpu_model: str
    gpu_vram_gb: float
    cuda_available: bool
    cuda_version: str
    opencv_version: str
    opencv_cuda_support: bool
    opencv_opencl_support: bool
    platform_info: str

@dataclass
class ProgressStats:
    """Real-time progress tracking statistics"""
    total_configs: int
    processed_configs: int
    skipped_configs: int
    high_confidence_count: int
    best_similarity: float
    start_time: float
    last_update_time: float
    processing_rate: float
    estimated_remaining_time: float
    active_threads: int
    current_config_id: str

class ProgressMonitor:
    """Real-time progress monitoring and display"""

    def __init__(self, total_configs: int, update_interval: float = 12.0):
        self.total_configs = total_configs
        self.update_interval = update_interval
        self.start_time = time.time()
        self.last_update = 0
        self.processed_count = 0
        self.skipped_count = 0
        self.high_confidence_count = 0
        self.best_similarity = 0.0
        self.current_config = ""
        self.active_threads = 0
        self.rate_history = deque(maxlen=10)  # Keep last 10 rate measurements
        self.lock = threading.Lock()

    def update(self, processed: int, skipped: int, high_confidence: int,
               best_sim: float, current_config: str, active_threads: int):
        """Update progress statistics"""
        with self.lock:
            self.processed_count = processed
            self.skipped_count = skipped
            self.high_confidence_count = high_confidence
            self.best_similarity = best_sim
            self.current_config = current_config
            self.active_threads = active_threads

            current_time = time.time()
            if current_time - self.last_update >= self.update_interval:
                self._display_progress()
                self.last_update = current_time

    def _display_progress(self):
        """Display current progress information"""
        current_time = time.time()
        elapsed = current_time - self.start_time
        total_processed = self.processed_count + self.skipped_count

        # Calculate processing rate
        if elapsed > 0:
            rate = total_processed / elapsed
            self.rate_history.append(rate)
            avg_rate = sum(self.rate_history) / len(self.rate_history)
        else:
            avg_rate = 0

        # Calculate progress percentage
        progress_pct = (total_processed / self.total_configs) * 100 if self.total_configs > 0 else 0

        # Estimate remaining time
        remaining_configs = self.total_configs - total_processed
        eta_seconds = remaining_configs / avg_rate if avg_rate > 0 else 0
        eta_str = self._format_time(eta_seconds)

        # Display progress
        print(f"\n{'='*80}")
        print(f"🚀 REAL-TIME PROGRESS MONITOR")
        print(f"{'='*80}")
        print(f"📊 Progress: {total_processed:,}/{self.total_configs:,} configs ({progress_pct:.1f}%)")
        print(f"✅ Processed: {self.processed_count:,} | ⏭️ Skipped: {self.skipped_count:,}")
        print(f"🎯 High Confidence: {self.high_confidence_count} | 🏆 Best: {self.best_similarity:.3f}")
        print(f"⚡ Rate: {avg_rate:.1f} configs/sec | 🕒 ETA: {eta_str}")
        print(f"🧵 Active Threads: {self.active_threads} | ⏱️ Elapsed: {self._format_time(elapsed)}")
        print(f"🔧 Current: {self.current_config}")
        print(f"{'='*80}")

    def _format_time(self, seconds: float) -> str:
        """Format time in human-readable format"""
        if seconds < 60:
            return f"{seconds:.0f}s"
        elif seconds < 3600:
            return f"{seconds//60:.0f}m {seconds%60:.0f}s"
        else:
            hours = seconds // 3600
            minutes = (seconds % 3600) // 60
            return f"{hours:.0f}h {minutes:.0f}m"

    def final_summary(self):
        """Display final progress summary"""
        elapsed = time.time() - self.start_time
        total_processed = self.processed_count + self.skipped_count
        avg_rate = total_processed / elapsed if elapsed > 0 else 0

        print(f"\n🏁 FINAL PROGRESS SUMMARY")
        print(f"⏱️ Total Time: {self._format_time(elapsed)}")
        print(f"📊 Configs Processed: {self.processed_count:,}")
        print(f"⏭️ Configs Skipped: {self.skipped_count:,}")
        print(f"⚡ Average Rate: {avg_rate:.1f} configs/sec")
        print(f"🎯 High Confidence Results: {self.high_confidence_count}")
        print(f"🏆 Best Similarity: {self.best_similarity:.3f}")

class SystemProfiler:
    """System information and GPU detection"""

    @staticmethod
    def get_system_info() -> SystemInfo:
        """Gather comprehensive system information"""
        # CPU Information
        cpu_count = os.cpu_count() or 4
        try:
            with open('/proc/cpuinfo', 'r') as f:
                cpu_info = f.read()
                cpu_model = "Unknown"
                for line in cpu_info.split('\n'):
                    if 'model name' in line:
                        cpu_model = line.split(':')[1].strip()
                        break
        except:
            cpu_model = platform.processor() or "Unknown"

        # Memory Information
        memory = psutil.virtual_memory()
        total_ram_gb = memory.total / (1024**3)
        available_ram_gb = memory.available / (1024**3)

        # GPU Information
        gpu_info = SystemProfiler._detect_gpu()

        # OpenCV Information
        opencv_info = SystemProfiler._get_opencv_info()

        return SystemInfo(
            cpu_count=cpu_count,
            cpu_model=cpu_model,
            total_ram_gb=total_ram_gb,
            available_ram_gb=available_ram_gb,
            gpu_model=gpu_info['model'],
            gpu_vram_gb=gpu_info['vram_gb'],
            cuda_available=gpu_info['cuda_available'],
            cuda_version=gpu_info['cuda_version'],
            opencv_version=opencv_info['version'],
            opencv_cuda_support=opencv_info['cuda_support'],
            opencv_opencl_support=opencv_info['opencl_support'],
            platform_info=f"{platform.system()} {platform.release()}"
        )

    @staticmethod
    def _detect_gpu() -> Dict:
        """Enhanced GPU detection with proper RTX 4090 support"""
        gpu_info = {
            'model': 'No GPU detected',
            'vram_gb': 0.0,
            'cuda_available': False,
            'cuda_version': 'N/A',
            'opencv_cuda_devices': 0
        }

        # First check OpenCV CUDA support
        try:
            cuda_devices = cv2.cuda.getCudaEnabledDeviceCount()
            gpu_info['opencv_cuda_devices'] = cuda_devices
            if cuda_devices > 0:
                gpu_info['cuda_available'] = True
                print(f"🎮 OpenCV detected {cuda_devices} CUDA device(s)")
        except Exception as e:
            print(f"⚠️ OpenCV CUDA check failed: {e}")

        # Try nvidia-smi for detailed GPU info
        try:
            result = subprocess.run(['nvidia-smi', '--query-gpu=name,memory.total',
                                   '--format=csv,noheader,nounits'],
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                if lines and lines[0]:
                    parts = lines[0].split(', ')
                    gpu_info['model'] = parts[0].strip()
                    if len(parts) > 1:
                        try:
                            gpu_info['vram_gb'] = float(parts[1]) / 1024
                        except:
                            gpu_info['vram_gb'] = 0.0
                    print(f"🚀 NVIDIA GPU detected: {gpu_info['model']}")
        except Exception as e:
            print(f"⚠️ nvidia-smi check failed: {e}")

        # Get CUDA version
        try:
            result = subprocess.run(['nvcc', '--version'],
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                for line in result.stdout.split('\n'):
                    if 'release' in line.lower():
                        gpu_info['cuda_version'] = line.split('release')[1].split(',')[0].strip()
                        break
        except Exception as e:
            print(f"⚠️ CUDA version check failed: {e}")
            if gpu_info['cuda_available']:
                gpu_info['cuda_version'] = 'Available (version unknown)'

        # Test actual CUDA functionality
        if gpu_info['cuda_available']:
            try:
                # Try to create a simple CUDA operation
                test_img = cv2.cuda_GpuMat()
                test_img.upload(np.zeros((100, 100), dtype=np.uint8))
                result = test_img.download()
                print(f"✅ CUDA functionality test passed")
                gpu_info['cuda_functional'] = True
            except Exception as e:
                print(f"❌ CUDA functionality test failed: {e}")
                gpu_info['cuda_functional'] = False
                gpu_info['cuda_available'] = False

        return gpu_info

    @staticmethod
    def _get_opencv_info() -> Dict:
        """Get OpenCV build and acceleration information"""
        opencv_info = {
            'version': cv2.__version__,
            'cuda_support': False,
            'opencl_support': False
        }

        # Check CUDA support
        try:
            opencv_info['cuda_support'] = cv2.cuda.getCudaEnabledDeviceCount() > 0
        except:
            pass

        # Check OpenCL support
        try:
            opencv_info['opencl_support'] = cv2.ocl.haveOpenCL()
        except:
            pass

        return opencv_info

    @staticmethod
    def display_system_info(info: SystemInfo):
        """Display comprehensive system information"""
        print(f"\n{'='*80}")
        print(f"🖥️  SYSTEM INFORMATION & CAPABILITIES")
        print(f"{'='*80}")
        print(f"💻 Platform: {info.platform_info}")
        print(f"🔧 CPU: {info.cpu_model}")
        print(f"⚙️  CPU Cores: {info.cpu_count}")
        print(f"🧠 RAM: {info.available_ram_gb:.1f}GB / {info.total_ram_gb:.1f}GB available")
        print(f"🎮 GPU: {info.gpu_model}")
        if info.gpu_vram_gb > 0:
            print(f"💾 VRAM: {info.gpu_vram_gb:.1f}GB")
        print(f"🚀 CUDA: {'✅ Available' if info.cuda_available else '❌ Not Available'}")
        if info.cuda_available:
            print(f"🔢 CUDA Version: {info.cuda_version}")
        print(f"📷 OpenCV: {info.opencv_version}")
        print(f"⚡ OpenCV CUDA: {'✅ Enabled' if info.opencv_cuda_support else '❌ Disabled'}")
        print(f"🌐 OpenCV OpenCL: {'✅ Enabled' if info.opencv_opencl_support else '❌ Disabled'}")
        print(f"{'='*80}")

class EnhancedMultithreadedOCRValidator:
    """
    Enhanced multithreaded OCR processor with real-time monitoring, GPU acceleration, and comprehensive testing
    """

    def __init__(self,
                 image_folder: str = ".",
                 reference_file: str = "rsa_private_key.pem",
                 max_workers: int = None,
                 use_gpu: bool = True,
                 use_processes: bool = False):
        """
        Initialize the enhanced OCR validator with advanced monitoring

        Args:
            image_folder: Directory containing images to process
            reference_file: Path to reference file for validation
            max_workers: Maximum number of worker threads/processes (default: all CPU cores)
            use_gpu: Whether to attempt GPU acceleration for preprocessing
            use_processes: Use ProcessPoolExecutor instead of ThreadPoolExecutor
        """
        self.image_folder = Path(image_folder)
        self.reference_file = Path(reference_file)
        self.use_processes = use_processes

        # Get system information
        self.system_info = SystemProfiler.get_system_info()

        # Use all available CPU cores for maximum performance
        self.max_workers = max_workers or self.system_info.cpu_count

        # GPU acceleration settings with enhanced detection
        self.use_gpu = use_gpu
        self.gpu_available = self._enhanced_gpu_check()

        # Load reference lines
        self.reference_lines = self._load_reference_lines()

        # Supported image formats
        self.supported_formats = {'.jpg', '.jpeg', '.png', '.tiff', '.tif', '.bmp', '.gif', '.webp'}

        # Results storage with thread safety
        self.results = []
        self.high_confidence_results = []
        self.lock = threading.Lock()
        self.best_similarity = 0.0
        self.early_termination_found = False

        # Enhanced performance tracking
        self.start_time = None
        self.processed_configs = 0
        self.skipped_configs = 0
        self.active_workers = set()
        self.worker_lock = threading.Lock()

        # Progress monitoring
        self.progress_monitor = None

        # Generate comprehensive OCR configurations
        self.ocr_configs = self._generate_comprehensive_configurations()

        # Display system information
        SystemProfiler.display_system_info(self.system_info)

        # Log initialization details
        executor_type = "ProcessPoolExecutor" if use_processes else "ThreadPoolExecutor"
        logger.info(f"🚀 Enhanced OCR Validator initialized with {executor_type}")
        logger.info(f"💻 Using {self.max_workers} {'processes' if use_processes else 'threads'} for maximum performance")
        logger.info(f"🎯 High confidence threshold: {HIGH_CONFIDENCE_THRESHOLD:.1%}")
        logger.info(f"📊 Total configurations to test: {len(self.ocr_configs)}")
        logger.info(f"🖥️  GPU acceleration: {'Available' if self.gpu_available else 'Not available'}")
        logger.info(f"📝 Reference lines loaded: {len(self.reference_lines)}")

    def _enhanced_gpu_check(self) -> bool:
        """Enhanced GPU detection with RTX 4090 specific optimizations"""
        gpu_available = False

        # Check CUDA availability (RTX 4090 support)
        try:
            cuda_devices = cv2.cuda.getCudaEnabledDeviceCount()
            if cuda_devices > 0:
                logger.info(f"🎮 CUDA GPU detected: {cuda_devices} device(s)")
                logger.info(f"🚀 GPU Model: {self.system_info.gpu_model}")
                logger.info(f"💾 VRAM: {self.system_info.gpu_vram_gb:.1f}GB")

                # RTX 4090 specific optimizations
                if "RTX 4090" in self.system_info.gpu_model or "4090" in self.system_info.gpu_model:
                    logger.info("🏆 RTX 4090 detected - enabling high-performance CUDA optimizations")
                    # Set optimal CUDA stream parameters for RTX 4090
                    try:
                        cv2.cuda.setDevice(0)
                        logger.info("✅ RTX 4090 CUDA device activated")
                    except Exception as e:
                        logger.warning(f"⚠️ RTX 4090 optimization failed: {e}")

                gpu_available = True
        except Exception as e:
            logger.debug(f"CUDA check failed: {e}")

        # Check OpenCL as fallback
        if not gpu_available:
            try:
                if cv2.ocl.haveOpenCL():
                    cv2.ocl.setUseOpenCL(True)
                    logger.info("⚡ OpenCL GPU acceleration enabled as fallback")
                    gpu_available = True
            except Exception as e:
                logger.debug(f"OpenCL check failed: {e}")

        if not gpu_available:
            logger.info("💻 GPU acceleration not available, using CPU only")

        return gpu_available

    def _register_worker(self, worker_id: str):
        """Register active worker for monitoring"""
        with self.worker_lock:
            self.active_workers.add(worker_id)

    def _unregister_worker(self, worker_id: str):
        """Unregister worker when task completes"""
        with self.worker_lock:
            self.active_workers.discard(worker_id)

    def _get_active_worker_count(self) -> int:
        """Get current number of active workers"""
        with self.worker_lock:
            return len(self.active_workers)
    
    def _load_reference_lines(self) -> List[str]:
        """Load the first 4 lines from the reference file"""
        try:
            with open(self.reference_file, 'r', encoding='utf-8') as f:
                lines = [line.rstrip('\n\r') for line in f.readlines()[:4]]
            
            if len(lines) < 4:
                logger.warning(f"Reference file has only {len(lines)} lines, expected 4")
            
            logger.info("Reference lines:")
            for i, line in enumerate(lines, 1):
                logger.info(f"  {i}: {line}")
            
            return lines
            
        except Exception as e:
            logger.error(f"Failed to load reference file {self.reference_file}: {e}")
            return []
    
    def _generate_comprehensive_configurations(self) -> List[Dict]:
        """Generate comprehensive OCR configurations with intelligent prioritization"""
        configs = []

        # Expanded scaling factors for higher resolution processing
        scale_factors = [1.0, 1.5, 2.0, 2.5, 3.0, 3.5, 4.0]

        # Comprehensive preprocessing methods
        preprocessing_methods = [
            # Basic scaling with different thresholding
            {'name': 'otsu_thresh', 'threshold': 'otsu', 'denoise': False},
            {'name': 'adaptive_thresh', 'threshold': 'adaptive', 'denoise': False},
            {'name': 'binary_thresh', 'threshold': 'binary', 'denoise': False},
            {'name': 'triangle_thresh', 'threshold': 'triangle', 'denoise': False},

            # Denoising combinations
            {'name': 'fastNl_denoise', 'denoise': 'fastNl', 'threshold': 'otsu'},
            {'name': 'bilateral_denoise', 'denoise': 'bilateral', 'threshold': 'otsu'},
            {'name': 'gaussian_denoise', 'denoise': 'gaussian', 'threshold': 'adaptive'},
            {'name': 'median_denoise', 'denoise': 'median', 'threshold': 'otsu'},

            # Enhancement techniques
            {'name': 'contrast_enhance', 'contrast': 1.5, 'threshold': 'otsu'},
            {'name': 'histogram_eq', 'hist_eq': True, 'threshold': 'otsu'},
            {'name': 'clahe', 'clahe': True, 'threshold': 'adaptive'},

            # Sharpening and filtering
            {'name': 'unsharp_mask', 'sharpen': 'unsharp', 'threshold': 'otsu'},
            {'name': 'laplacian_sharp', 'sharpen': 'laplacian', 'threshold': 'otsu'},
            {'name': 'gaussian_sharp', 'sharpen': 'gaussian', 'threshold': 'adaptive'},

            # Morphological operations
            {'name': 'morph_close', 'morphology': 'close', 'threshold': 'otsu'},
            {'name': 'morph_open', 'morphology': 'open', 'threshold': 'otsu'},
            {'name': 'morph_gradient', 'morphology': 'gradient', 'threshold': 'adaptive'},

            # Combined advanced techniques
            {'name': 'advanced_combo1', 'denoise': 'fastNl', 'contrast': 1.3, 'sharpen': 'unsharp', 'threshold': 'otsu'},
            {'name': 'advanced_combo2', 'clahe': True, 'denoise': 'bilateral', 'threshold': 'adaptive'},
            {'name': 'advanced_combo3', 'hist_eq': True, 'sharpen': 'laplacian', 'morphology': 'close', 'threshold': 'otsu'},
        ]

        # All Tesseract PSM modes (0-13) and OEM modes (0-3)
        psm_modes = list(range(14))  # PSM 0-13
        oem_modes = list(range(4))   # OEM 0-3

        # Character whitelists for different scenarios
        whitelists = [
            None,  # No whitelist
            'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=-',  # Base64 + headers
            'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789',  # Alphanumeric only
            'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789+/=-',  # Uppercase + base64 chars
        ]

        # Priority configurations based on previous successful results
        priority_configs = [
            # Best performing from previous tests
            {'scale': 3.0, 'preprocessing': 'otsu_thresh', 'psm': 6, 'oem': 3, 'priority': 1},
            {'scale': 3.0, 'preprocessing': 'otsu_thresh', 'psm': 6, 'oem': 1, 'priority': 1},
            {'scale': 2.0, 'preprocessing': 'otsu_thresh', 'psm': 6, 'oem': 3, 'priority': 1},
            {'scale': 2.5, 'preprocessing': 'advanced_combo1', 'psm': 6, 'oem': 3, 'priority': 1},
            {'scale': 3.5, 'preprocessing': 'fastNl_denoise', 'psm': 6, 'oem': 1, 'priority': 1},
        ]

        # Generate priority configurations first
        for priority_config in priority_configs:
            scale = priority_config['scale']
            prep_name = priority_config['preprocessing']
            prep_method = next((p for p in preprocessing_methods if p['name'] == prep_name), preprocessing_methods[0])

            for whitelist in whitelists[:2]:  # Use top 2 whitelists for priority configs
                config = {
                    'preprocessing': {**prep_method, 'scale': scale},
                    'tesseract': {
                        'psm': priority_config['psm'],
                        'oem': priority_config['oem'],
                        'whitelist': whitelist
                    },
                    'priority': priority_config['priority'],
                    'config_id': f"priority_{prep_name}_s{scale}_psm{priority_config['psm']}_oem{priority_config['oem']}"
                }
                configs.append(config)

        # Generate comprehensive configurations for thorough testing
        for scale in scale_factors:
            for prep_method in preprocessing_methods:
                for psm in [6, 7, 8, 11, 13]:  # Focus on most relevant PSM modes first
                    for oem in [1, 3]:  # Most stable OEM modes
                        for whitelist in whitelists:
                            config = {
                                'preprocessing': {**prep_method, 'scale': scale},
                                'tesseract': {
                                    'psm': psm,
                                    'oem': oem,
                                    'whitelist': whitelist
                                },
                                'priority': 2,
                                'config_id': f"{prep_method['name']}_s{scale}_psm{psm}_oem{oem}_{hash(str(whitelist))%1000}"
                            }
                            configs.append(config)

        # Add remaining PSM modes for completeness
        for scale in [2.0, 3.0]:  # Focus on best scales
            for prep_method in preprocessing_methods[:5]:  # Top preprocessing methods
                for psm in [0, 1, 2, 3, 4, 5, 9, 10, 12]:  # Remaining PSM modes
                    for oem in [0, 2]:  # Remaining OEM modes
                        config = {
                            'preprocessing': {**prep_method, 'scale': scale},
                            'tesseract': {
                                'psm': psm,
                                'oem': oem,
                                'whitelist': whitelists[1]  # Use base64 whitelist
                            },
                            'priority': 3,
                            'config_id': f"{prep_method['name']}_s{scale}_psm{psm}_oem{oem}_complete"
                        }
                        configs.append(config)

        # Sort by priority (lower number = higher priority)
        configs.sort(key=lambda x: (x['priority'], x['config_id']))

        logger.info(f"📋 Generated {len(configs)} comprehensive OCR configurations")
        logger.info(f"🎯 Priority configurations: {len([c for c in configs if c['priority'] == 1])}")
        logger.info(f"🔍 Standard configurations: {len([c for c in configs if c['priority'] == 2])}")
        logger.info(f"🧪 Extended configurations: {len([c for c in configs if c['priority'] == 3])}")

        return configs
    
    def find_image_files(self) -> List[Path]:
        """Find all supported image files in the folder"""
        image_files = []
        
        try:
            for file_path in self.image_folder.iterdir():
                if file_path.is_file() and file_path.suffix.lower() in self.supported_formats:
                    image_files.append(file_path)
            
            logger.info(f"Found {len(image_files)} image files")
            for img_file in image_files:
                logger.info(f"  - {img_file.name}")
                
        except Exception as e:
            logger.error(f"Error scanning image folder: {e}")
        
        return image_files
    
    def preprocess_image_enhanced(self, image_path: Path, config: Dict) -> Optional[np.ndarray]:
        """Apply enhanced preprocessing with comprehensive techniques"""
        try:
            # Load image
            img = cv2.imread(str(image_path))
            if img is None:
                logger.error(f"Could not load image: {image_path}")
                return None

            # Convert to grayscale
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

            # Apply scaling with high-quality interpolation
            scale = config.get('scale', 1.0)
            if scale != 1.0:
                height, width = gray.shape
                new_width = int(width * scale)
                new_height = int(height * scale)

                # Use different interpolation methods based on scale
                if scale > 1.0:
                    interpolation = cv2.INTER_CUBIC  # Better for upscaling
                else:
                    interpolation = cv2.INTER_AREA   # Better for downscaling

                gray = cv2.resize(gray, (new_width, new_height), interpolation=interpolation)

            # Apply advanced denoising
            denoise_type = config.get('denoise')
            if denoise_type == 'fastNl':
                gray = cv2.fastNlMeansDenoising(gray, h=10, templateWindowSize=7, searchWindowSize=21)
            elif denoise_type == 'bilateral':
                gray = cv2.bilateralFilter(gray, 9, 75, 75)
            elif denoise_type == 'gaussian':
                gray = cv2.GaussianBlur(gray, (5, 5), 0)
            elif denoise_type == 'median':
                gray = cv2.medianBlur(gray, 5)

            # Apply contrast enhancement
            if config.get('contrast'):
                contrast_factor = config['contrast']
                gray = cv2.convertScaleAbs(gray, alpha=contrast_factor, beta=0)

            # Apply histogram equalization
            if config.get('hist_eq'):
                gray = cv2.equalizeHist(gray)

            # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization)
            if config.get('clahe'):
                clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
                gray = clahe.apply(gray)

            # Apply advanced thresholding
            threshold_type = config.get('threshold')
            if threshold_type == 'otsu':
                _, gray = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            elif threshold_type == 'adaptive':
                gray = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                           cv2.THRESH_BINARY, 11, 2)
            elif threshold_type == 'binary':
                _, gray = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
            elif threshold_type == 'triangle':
                _, gray = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_TRIANGLE)

            # Apply advanced sharpening
            sharpen_type = config.get('sharpen')
            if sharpen_type == 'unsharp':
                # Unsharp masking
                gaussian = cv2.GaussianBlur(gray, (0, 0), 2.0)
                gray = cv2.addWeighted(gray, 1.5, gaussian, -0.5, 0)
            elif sharpen_type == 'laplacian':
                # Laplacian sharpening
                laplacian = cv2.Laplacian(gray, cv2.CV_64F)
                gray = np.uint8(np.clip(gray + 0.3 * laplacian, 0, 255))
            elif sharpen_type == 'gaussian':
                # Gaussian-based sharpening
                kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
                gray = cv2.filter2D(gray, -1, kernel)

            # Apply morphological operations
            morph_type = config.get('morphology')
            if morph_type:
                kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
                if morph_type == 'close':
                    gray = cv2.morphologyEx(gray, cv2.MORPH_CLOSE, kernel)
                elif morph_type == 'open':
                    gray = cv2.morphologyEx(gray, cv2.MORPH_OPEN, kernel)
                elif morph_type == 'gradient':
                    gray = cv2.morphologyEx(gray, cv2.MORPH_GRADIENT, kernel)

            # Enhanced GPU acceleration for RTX 4090 and other CUDA GPUs
            gpu_used = False
            if self.gpu_available and gray.shape[0] * gray.shape[1] > 100000:  # Process smaller images on GPU too
                try:
                    print(f"🎮 Attempting GPU acceleration for {config.get('name', 'unknown')} preprocessing...")

                    # Upload to GPU
                    gpu_img = cv2.cuda_GpuMat()
                    gpu_img.upload(gray)

                    # Apply GPU-accelerated operations
                    if config.get('denoise') == 'fastNl':
                        print(f"🚀 Using GPU for fastNl denoising...")
                        # GPU-accelerated denoising
                        gpu_denoised = cv2.cuda.fastNlMeansDenoising(gpu_img, h=10.0)
                        gpu_img = gpu_denoised
                        gpu_used = True

                    if config.get('threshold') == 'otsu':
                        print(f"🚀 Using GPU for Otsu thresholding...")
                        # GPU-accelerated thresholding
                        _, gpu_thresh = cv2.cuda.threshold(gpu_img, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
                        gpu_img = gpu_thresh
                        gpu_used = True

                    # Always try GPU resize if scaling
                    if config.get('scale', 1.0) != 1.0:
                        print(f"🚀 Using GPU for image scaling...")
                        height, width = gray.shape
                        new_width = int(width * config['scale'])
                        new_height = int(height * config['scale'])
                        gpu_resized = cv2.cuda.resize(gpu_img, (new_width, new_height))
                        gpu_img = gpu_resized
                        gpu_used = True

                    # Download result from GPU
                    if gpu_used:
                        gray = gpu_img.download()
                        print(f"✅ GPU acceleration successfully applied for {config.get('name', 'unknown')}")
                        logger.info(f"🎮 GPU acceleration used for {config.get('name', 'unknown')} preprocessing")

                except Exception as gpu_error:
                    print(f"❌ GPU processing failed: {gpu_error}")
                    logger.warning(f"⚠️ GPU processing failed, using CPU fallback: {gpu_error}")
                    # Continue with CPU processing - gray image is already processed above
                    gpu_used = False

            # Add GPU usage info to result
            if hasattr(self, '_current_result'):
                self._current_result['gpu_used'] = gpu_used

            return gray

        except Exception as e:
            logger.error(f"Error in enhanced preprocessing for {image_path}: {e}")
            return None
    
    def extract_text_with_tesseract(self, image: np.ndarray, config: Dict) -> str:
        """Extract text using Tesseract with specific configuration"""
        try:
            # Build Tesseract config string
            psm = config.get('psm', 6)
            oem = config.get('oem', 3)
            whitelist = config.get('whitelist')
            
            tess_config = f'--oem {oem} --psm {psm}'
            if whitelist:
                tess_config += f' -c tessedit_char_whitelist={whitelist}'
            
            # Convert numpy array to PIL Image for Tesseract
            pil_image = Image.fromarray(image)
            
            # Extract text
            text = pytesseract.image_to_string(pil_image, config=tess_config)
            return text.strip()
            
        except Exception as e:
            logger.error(f"Error extracting text with Tesseract: {e}")
            return ""
    
    def calculate_similarity_with_threshold(self, ocr_lines: List[str]) -> Dict[str, float]:
        """Calculate similarity with 95% threshold checking for early termination"""
        if not self.reference_lines or not ocr_lines:
            return {
                'average': 0.0,
                'line_similarities': [],
                'meets_threshold': False,
                'first_four_average': 0.0
            }

        line_similarities = []

        # Compare first 4 lines (critical for RSA key validation)
        max_lines = min(len(self.reference_lines), len(ocr_lines), 4)

        for i in range(max_lines):
            ref_line = self.reference_lines[i]
            ocr_line = ocr_lines[i] if i < len(ocr_lines) else ""

            # Calculate character-level similarity with enhanced matching
            if len(ref_line) == 0 and len(ocr_line) == 0:
                similarity = 1.0
            elif len(ref_line) == 0 or len(ocr_line) == 0:
                similarity = 0.0
            else:
                # Use difflib for sequence matching with optimized parameters
                matcher = difflib.SequenceMatcher(None, ref_line, ocr_line)
                similarity = matcher.ratio()

                # Apply ground truth accuracy adjustment (98% reference accuracy)
                # If similarity is very high, account for potential reference errors
                if similarity > 0.95:
                    adjusted_similarity = min(1.0, similarity / GROUND_TRUTH_ACCURACY)
                    similarity = adjusted_similarity

            line_similarities.append(similarity)

            # Early termination check: if any of the first 4 lines is below threshold
            if similarity < HIGH_CONFIDENCE_THRESHOLD:
                # Return immediately to save processing time
                return {
                    'average': similarity,
                    'line_similarities': line_similarities,
                    'meets_threshold': False,
                    'first_four_average': sum(line_similarities) / len(line_similarities),
                    'early_termination': True,
                    'failed_line': i + 1
                }

        # Calculate average similarity for first 4 lines
        first_four_average = sum(line_similarities) / len(line_similarities) if line_similarities else 0.0

        # Check if all first 4 lines meet the high confidence threshold
        meets_threshold = all(sim >= HIGH_CONFIDENCE_THRESHOLD for sim in line_similarities)

        # If we have more lines, calculate overall average
        if len(ocr_lines) > 4:
            # Calculate similarity for remaining lines (less critical)
            for i in range(4, min(len(self.reference_lines), len(ocr_lines))):
                ref_line = self.reference_lines[i]
                ocr_line = ocr_lines[i]

                if len(ref_line) == 0 and len(ocr_line) == 0:
                    similarity = 1.0
                elif len(ref_line) == 0 or len(ocr_line) == 0:
                    similarity = 0.0
                else:
                    matcher = difflib.SequenceMatcher(None, ref_line, ocr_line)
                    similarity = matcher.ratio()

                line_similarities.append(similarity)

        # Overall average (weighted towards first 4 lines)
        if len(line_similarities) > 4:
            # Weight first 4 lines more heavily (80% weight)
            first_four_weight = 0.8
            remaining_weight = 0.2

            first_four_avg = sum(line_similarities[:4]) / 4
            remaining_avg = sum(line_similarities[4:]) / len(line_similarities[4:])

            overall_average = (first_four_avg * first_four_weight +
                             remaining_avg * remaining_weight)
        else:
            overall_average = first_four_average

        return {
            'average': overall_average,
            'line_similarities': line_similarities,
            'meets_threshold': meets_threshold,
            'first_four_average': first_four_average,
            'early_termination': False
        }
    
    def is_perfect_match(self, ocr_lines: List[str]) -> bool:
        """Check if OCR results perfectly match the first 4 reference lines"""
        if len(ocr_lines) < 4 or len(self.reference_lines) < 4:
            return False

        return all(ocr_lines[i] == self.reference_lines[i] for i in range(4))

    def process_single_image_config_enhanced(self, image_path: Path, config: Dict) -> Optional[Dict]:
        """Process a single image with enhanced configuration, monitoring, and early termination"""
        start_time = time.time()
        worker_id = f"worker_{threading.current_thread().ident}_{int(time.time()*1000)%10000}"

        # Register worker for monitoring
        self._register_worker(worker_id)

        try:
            # Check if we should skip this configuration due to early termination
            with self.lock:
                if self.early_termination_found and config.get('priority', 3) > 1:
                    self.skipped_configs += 1
                    return None
            # Preprocess image with enhanced techniques
            processed_image = self.preprocess_image_enhanced(image_path, config['preprocessing'])
            if processed_image is None:
                return {
                    'image_path': str(image_path),
                    'config_id': config['config_id'],
                    'error': 'Failed to preprocess image',
                    'processing_time': time.time() - start_time,
                    'priority': config.get('priority', 3)
                }

            # Extract text with enhanced Tesseract configuration
            raw_text = self.extract_text_with_tesseract(processed_image, config['tesseract'])

            # Parse lines more intelligently
            lines = []
            for line in raw_text.split('\n'):
                cleaned = line.strip()
                if cleaned and len(cleaned) > 3:  # Filter out very short lines
                    lines.append(cleaned)

            # Calculate metrics with threshold checking
            similarity_metrics = self.calculate_similarity_with_threshold(lines)

            # Early termination if below threshold
            if similarity_metrics.get('early_termination', False):
                return {
                    'image_path': str(image_path),
                    'config_id': config['config_id'],
                    'preprocessing': config['preprocessing'],
                    'tesseract': config['tesseract'],
                    'raw_text': raw_text[:200] + '...' if len(raw_text) > 200 else raw_text,  # Truncate for memory
                    'extracted_lines': lines[:4],  # Only first 4 lines
                    'total_lines': len(lines),
                    'similarity_average': similarity_metrics['average'],
                    'first_four_average': similarity_metrics['first_four_average'],
                    'line_similarities': similarity_metrics['line_similarities'],
                    'meets_threshold': False,
                    'early_termination': True,
                    'failed_line': similarity_metrics.get('failed_line'),
                    'processing_time': time.time() - start_time,
                    'timestamp': datetime.now().isoformat(),
                    'priority': config.get('priority', 3)
                }

            # Check for high confidence result
            meets_threshold = similarity_metrics['meets_threshold']
            first_four_avg = similarity_metrics['first_four_average']

            # Update global best similarity
            with self.lock:
                if first_four_avg > self.best_similarity:
                    self.best_similarity = first_four_avg
                    logger.info(f"🎯 New best similarity: {first_four_avg:.3f} with {config['config_id']}")

                # Check for early termination condition
                if meets_threshold and not self.early_termination_found:
                    self.early_termination_found = True
                    logger.info(f"🏆 HIGH CONFIDENCE RESULT FOUND! Similarity: {first_four_avg:.3f}")
                    logger.info(f"🚀 Continuing with remaining priority configurations...")

            # Create comprehensive result
            result = {
                'image_path': str(image_path),
                'config_id': config['config_id'],
                'preprocessing': config['preprocessing'],
                'tesseract': config['tesseract'],
                'raw_text': raw_text,
                'extracted_lines': lines,
                'total_lines': len(lines),
                'similarity_average': similarity_metrics['average'],
                'first_four_average': first_four_avg,
                'line_similarities': similarity_metrics['line_similarities'],
                'meets_threshold': meets_threshold,
                'is_high_confidence': meets_threshold,
                'processing_time': time.time() - start_time,
                'timestamp': datetime.now().isoformat(),
                'priority': config.get('priority', 3)
            }

            # Store high confidence results separately
            if meets_threshold:
                with self.lock:
                    self.high_confidence_results.append(result)

            # Update processed count and progress monitoring
            with self.lock:
                self.processed_configs += 1

                # Update progress monitor if available
                if self.progress_monitor:
                    active_workers = self._get_active_worker_count()
                    self.progress_monitor.update(
                        processed=self.processed_configs,
                        skipped=self.skipped_configs,
                        high_confidence=len(self.high_confidence_results),
                        best_sim=self.best_similarity,
                        current_config=config['config_id'],
                        active_threads=active_workers
                    )

            return result

        except Exception as e:
            logger.error(f"Error processing {image_path} with config {config['config_id']}: {e}")
            return {
                'image_path': str(image_path),
                'config_id': config['config_id'],
                'error': str(e),
                'processing_time': time.time() - start_time,
                'priority': config.get('priority', 3)
            }
        finally:
            # Always unregister worker
            self._unregister_worker(worker_id)

    def process_single_image_enhanced(self, image_path: Path) -> List[Dict]:
        """Process a single image with enhanced configuration testing and early termination"""
        logger.info(f"🖼️  Processing image: {image_path.name}")

        image_results = []
        high_confidence_found = False

        # Process configurations in priority order
        for i, config in enumerate(self.ocr_configs):
            # Check if we should continue processing
            with self.lock:
                # Skip lower priority configs if high confidence result found and we've tested enough
                if (self.early_termination_found and
                    config.get('priority', 3) > 2 and
                    len(self.high_confidence_results) > 0):
                    self.skipped_configs += 1
                    continue

            # Process this configuration
            result = self.process_single_image_config_enhanced(image_path, config)

            if result is not None:
                image_results.append(result)

                # Check for high confidence result
                if result.get('is_high_confidence', False):
                    high_confidence_found = True
                    logger.info(f"✨ High confidence result for {image_path.name} with {config['config_id']}")
                    logger.info(f"📊 First 4 lines similarity: {result.get('first_four_average', 0):.3f}")

                # Progress reporting for long-running processes
                if i % 50 == 0 and i > 0:
                    with self.lock:
                        progress = (self.processed_configs / len(self.ocr_configs)) * 100
                        logger.info(f"📈 Progress: {progress:.1f}% ({self.processed_configs}/{len(self.ocr_configs)} configs)")
                        logger.info(f"🎯 Best similarity so far: {self.best_similarity:.3f}")
                        logger.info(f"⚡ High confidence results: {len(self.high_confidence_results)}")

        # Thread-safe result storage
        with self.lock:
            self.results.extend(image_results)

        logger.info(f"✅ Completed {image_path.name}: {len(image_results)} results, "
                   f"{len([r for r in image_results if r.get('is_high_confidence', False)])} high confidence")

        return image_results

    def run_enhanced_multithreaded_ocr(self) -> Dict:
        """Run enhanced OCR with real-time monitoring, GPU acceleration, and intelligent early termination"""
        self.start_time = time.time()

        # Find all image files
        image_files = self.find_image_files()

        if not image_files:
            logger.warning("No image files found to process")
            return {
                'total_images': 0,
                'total_attempts': 0,
                'high_confidence_matches': 0,
                'processing_time': 0,
                'results': []
            }

        # Initialize progress monitor
        total_configs = len(image_files) * len(self.ocr_configs)
        self.progress_monitor = ProgressMonitor(total_configs, update_interval=12.0)

        logger.info(f"🚀 Starting ENHANCED multithreaded OCR processing")
        executor_type = "ProcessPoolExecutor" if self.use_processes else "ThreadPoolExecutor"
        logger.info(f"💻 Using {executor_type} with {self.max_workers} {'processes' if self.use_processes else 'threads'}")
        logger.info(f"🖼️  Processing {len(image_files)} images")
        logger.info(f"⚙️  Testing up to {len(self.ocr_configs)} configurations per image")
        logger.info(f"📊 Total configurations: {total_configs:,}")
        logger.info(f"🎯 High confidence threshold: {HIGH_CONFIDENCE_THRESHOLD:.1%}")
        logger.info(f"🔬 Ground truth accuracy assumption: {GROUND_TRUTH_ACCURACY:.1%}")
        logger.info(f"🎮 GPU acceleration: {'Enabled' if self.gpu_available else 'Disabled'}")

        # Setup signal handler for graceful interruption
        def signal_handler(signum, frame):
            logger.info("🛑 Received interrupt signal, finishing current tasks...")
            self.early_termination_found = True

        signal.signal(signal.SIGINT, signal_handler)

        # Choose executor based on configuration
        executor_class = ProcessPoolExecutor if self.use_processes else ThreadPoolExecutor

        # Process configurations in parallel instead of images for better parallelization
        print(f"🚀 Submitting {len(self.ocr_configs)} configuration tasks to {self.max_workers} workers...")

        with executor_class(max_workers=self.max_workers) as executor:
            # Submit individual configuration tasks for better parallelization
            future_to_config = {}

            for image_path in image_files:
                for config in self.ocr_configs:
                    future = executor.submit(self.process_single_image_config_enhanced, image_path, config)
                    future_to_config[future] = (image_path, config)

            print(f"📊 Submitted {len(future_to_config)} total tasks")
            print(f"🧵 Using {self.max_workers} parallel workers")
            print(f"⏱️ Starting parallel execution...")

            # Collect results as they complete
            completed_tasks = 0
            total_tasks = len(future_to_config)

            for future in as_completed(future_to_config):
                image_path, config = future_to_config[future]
                try:
                    result = future.result()
                    completed_tasks += 1

                    if result is not None:
                        # Store result
                        with self.lock:
                            self.results.append(result)

                        # Log progress every 50 completed tasks
                        if completed_tasks % 50 == 0:
                            elapsed = time.time() - self.start_time
                            rate = completed_tasks / elapsed if elapsed > 0 else 0
                            progress_pct = (completed_tasks / total_tasks) * 100

                            print(f"📈 Progress: {completed_tasks}/{total_tasks} ({progress_pct:.1f}%)")
                            print(f"⚡ Rate: {rate:.1f} tasks/sec")
                            print(f"🎯 Best similarity so far: {self.best_similarity:.3f}")
                            print(f"� High confidence results: {len(self.high_confidence_results)}")

                            # Show active worker count
                            active_workers = self._get_active_worker_count()
                            print(f"🧵 Active workers: {active_workers}/{self.max_workers}")
                            print(f"💾 Memory: {psutil.virtual_memory().percent:.1f}%")
                            print("-" * 60)

                except Exception as e:
                    logger.error(f"❌ Error processing {image_path} with {config['config_id']}: {e}")
                    completed_tasks += 1

        # Final progress update
        if self.progress_monitor:
            self.progress_monitor.final_summary()

        # Calculate comprehensive summary statistics
        total_time = time.time() - self.start_time

        # Filter valid results
        valid_results = [r for r in self.results if 'similarity_average' in r and 'error' not in r]

        # Calculate various metrics
        high_confidence_matches = len(self.high_confidence_results)
        excellent_matches = sum(1 for r in valid_results if r.get('first_four_average', 0) >= 0.90)
        good_matches = sum(1 for r in valid_results if r.get('first_four_average', 0) >= 0.80)

        # Find best overall result
        best_overall = None
        if valid_results:
            best_overall = max(valid_results, key=lambda x: x.get('first_four_average', 0))

        # Performance metrics
        configs_per_second = self.processed_configs / total_time if total_time > 0 else 0
        time_saved = self.skipped_configs * (total_time / max(self.processed_configs, 1))

        summary = {
            'total_images': len(image_files),
            'total_configurations': len(self.ocr_configs),
            'processed_configurations': self.processed_configs,
            'skipped_configurations': self.skipped_configs,
            'total_attempts': len(self.results),
            'valid_attempts': len(valid_results),
            'high_confidence_matches': high_confidence_matches,
            'excellent_matches': excellent_matches,
            'good_matches': good_matches,
            'processing_time': total_time,
            'configs_per_second': configs_per_second,
            'time_saved_seconds': time_saved,
            'best_similarity': self.best_similarity,
            'best_overall_result': best_overall,
            'high_confidence_results': self.high_confidence_results,
            'early_termination_triggered': self.early_termination_found,
            'gpu_acceleration_used': self.gpu_available,
            'results': self.results
        }

        # Enhanced completion logging
        logger.info(f"🎉 ENHANCED OCR PROCESSING COMPLETED!")
        logger.info(f"⏱️  Total time: {total_time:.2f} seconds")
        logger.info(f"⚡ Processing speed: {configs_per_second:.1f} configurations/second")
        logger.info(f"🎯 High confidence matches (≥{HIGH_CONFIDENCE_THRESHOLD:.0%}): {high_confidence_matches}")
        logger.info(f"⭐ Excellent matches (≥90%): {excellent_matches}")
        logger.info(f"👍 Good matches (≥80%): {good_matches}")
        logger.info(f"🏆 Best similarity achieved: {self.best_similarity:.3f}")
        logger.info(f"⚡ Time saved by early termination: {time_saved:.1f} seconds")

        return summary

    def print_enhanced_summary(self, summary: Dict):
        """Print enhanced summary with detailed performance metrics"""
        print("\n" + "=" * 80)
        print("🎯 ENHANCED OCR VALIDATION SUMMARY")
        print("=" * 80)

        print(f"🖼️  Images processed: {summary['total_images']}")
        print(f"⚙️  Total configurations: {summary['total_configurations']}")
        print(f"✅ Processed configurations: {summary['processed_configurations']}")
        print(f"⏭️  Skipped configurations: {summary['skipped_configurations']}")
        print(f"🎯 High confidence matches (≥{HIGH_CONFIDENCE_THRESHOLD:.0%}): {summary['high_confidence_matches']}")
        print(f"⭐ Excellent matches (≥90%): {summary['excellent_matches']}")
        print(f"👍 Good matches (≥80%): {summary['good_matches']}")
        print(f"🏆 Best similarity achieved: {summary['best_similarity']:.3f} ({summary['best_similarity']*100:.1f}%)")
        print(f"⏱️  Processing time: {summary['processing_time']:.2f}s")
        print(f"⚡ Processing speed: {summary['configs_per_second']:.1f} configs/second")
        print(f"💾 Time saved by optimization: {summary['time_saved_seconds']:.1f}s")

        # Show top performing configurations
        if summary['high_confidence_results']:
            print(f"\n🏆 HIGH CONFIDENCE RESULTS (≥{HIGH_CONFIDENCE_THRESHOLD:.0%}):")
            sorted_results = sorted(summary['high_confidence_results'],
                                  key=lambda x: x.get('first_four_average', 0), reverse=True)
            for i, result in enumerate(sorted_results[:5], 1):
                print(f"   {i}. {result['config_id']}: {result.get('first_four_average', 0):.3f} "
                      f"({result['processing_time']:.2f}s)")

        # Show best overall result details
        if summary.get('best_overall_result'):
            best = summary['best_overall_result']
            print(f"\n🥇 BEST OVERALL CONFIGURATION:")
            print(f"   • ID: {best['config_id']}")
            print(f"   • First 4 lines accuracy: {best.get('first_four_average', 0):.3f}")
            print(f"   • Overall accuracy: {best.get('similarity_average', 0):.3f}")
            print(f"   • Processing time: {best['processing_time']:.2f}s")
            print(f"   • Priority level: {best.get('priority', 'N/A')}")

            # Show line-by-line accuracy
            line_sims = best.get('line_similarities', [])
            if line_sims:
                print(f"   • Line accuracies:")
                for i, sim in enumerate(line_sims[:4], 1):
                    status = "✅" if sim >= HIGH_CONFIDENCE_THRESHOLD else "⚠️" if sim >= 0.8 else "❌"
                    print(f"     Line {i}: {sim:.3f} {status}")

        print("\n" + "=" * 80)

    def save_enhanced_results(self, summary: Dict, output_prefix: str = "enhanced_ocr_validation") -> Tuple[str, str]:
        """Save enhanced results with comprehensive analysis"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Save comprehensive JSON results
        json_filename = f"{output_prefix}_{timestamp}.json"
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)

        # Generate enhanced text report
        txt_filename = f"{output_prefix}_{timestamp}.txt"
        report = self._generate_enhanced_report(summary)
        with open(txt_filename, 'w', encoding='utf-8') as f:
            f.write(report)

        logger.info(f"📄 Enhanced results saved to {json_filename} and {txt_filename}")
        return json_filename, txt_filename

    def _generate_enhanced_report(self, summary: Dict) -> str:
        """Generate comprehensive enhanced report"""
        report_lines = []

        # Header
        report_lines.append("=" * 100)
        report_lines.append("ENHANCED MULTITHREADED OCR VALIDATION REPORT")
        report_lines.append("High-Performance OCR with 95% Accuracy Threshold")
        report_lines.append("=" * 100)
        report_lines.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append(f"Reference file: {self.reference_file}")
        report_lines.append(f"High confidence threshold: {HIGH_CONFIDENCE_THRESHOLD:.1%}")
        report_lines.append(f"Ground truth accuracy assumption: {GROUND_TRUTH_ACCURACY:.1%}")
        report_lines.append("")

        # Performance Summary
        report_lines.append("PERFORMANCE SUMMARY")
        report_lines.append("-" * 50)
        report_lines.append(f"Total images processed: {summary['total_images']}")
        report_lines.append(f"Total configurations available: {summary['total_configurations']}")
        report_lines.append(f"Configurations processed: {summary['processed_configurations']}")
        report_lines.append(f"Configurations skipped (optimization): {summary['skipped_configurations']}")
        report_lines.append(f"Processing time: {summary['processing_time']:.2f} seconds")
        report_lines.append(f"Processing speed: {summary['configs_per_second']:.1f} configs/second")
        report_lines.append(f"Time saved by optimization: {summary['time_saved_seconds']:.1f} seconds")
        report_lines.append(f"GPU acceleration: {'Used' if summary['gpu_acceleration_used'] else 'Not available'}")
        report_lines.append(f"Early termination triggered: {'Yes' if summary['early_termination_triggered'] else 'No'}")
        report_lines.append("")

        # Accuracy Results
        report_lines.append("ACCURACY RESULTS")
        report_lines.append("-" * 50)
        report_lines.append(f"High confidence matches (≥{HIGH_CONFIDENCE_THRESHOLD:.0%}): {summary['high_confidence_matches']}")
        report_lines.append(f"Excellent matches (≥90%): {summary['excellent_matches']}")
        report_lines.append(f"Good matches (≥80%): {summary['good_matches']}")
        report_lines.append(f"Best similarity achieved: {summary['best_similarity']:.3f} ({summary['best_similarity']*100:.1f}%)")
        report_lines.append("")

        # Reference Lines
        report_lines.append("REFERENCE LINES (Ground Truth)")
        report_lines.append("-" * 50)
        for i, line in enumerate(self.reference_lines[:4], 1):
            report_lines.append(f"  {i}: {line}")
        report_lines.append("")

        # High Confidence Results
        if summary['high_confidence_results']:
            report_lines.append(f"HIGH CONFIDENCE RESULTS (≥{HIGH_CONFIDENCE_THRESHOLD:.0%})")
            report_lines.append("-" * 50)
            sorted_results = sorted(summary['high_confidence_results'],
                                  key=lambda x: x.get('first_four_average', 0), reverse=True)
            for i, result in enumerate(sorted_results, 1):
                report_lines.append(f"#{i}: {result['config_id']}")
                report_lines.append(f"    First 4 lines accuracy: {result.get('first_four_average', 0):.3f}")
                report_lines.append(f"    Overall accuracy: {result.get('similarity_average', 0):.3f}")
                report_lines.append(f"    Processing time: {result['processing_time']:.3f}s")
                report_lines.append(f"    Priority: {result.get('priority', 'N/A')}")

                # Line-by-line breakdown
                line_sims = result.get('line_similarities', [])
                if line_sims:
                    report_lines.append("    Line-by-line accuracy:")
                    for j, sim in enumerate(line_sims[:4], 1):
                        status = "EXCELLENT" if sim >= HIGH_CONFIDENCE_THRESHOLD else "GOOD" if sim >= 0.8 else "POOR"
                        report_lines.append(f"      Line {j}: {sim:.3f} ({status})")
                report_lines.append("")

        # Best Overall Configuration Details
        if summary.get('best_overall_result'):
            best = summary['best_overall_result']
            report_lines.append("BEST OVERALL CONFIGURATION")
            report_lines.append("-" * 50)
            report_lines.append(f"Configuration ID: {best['config_id']}")
            report_lines.append(f"First 4 lines accuracy: {best.get('first_four_average', 0):.3f}")
            report_lines.append(f"Overall accuracy: {best.get('similarity_average', 0):.3f}")
            report_lines.append(f"Processing time: {best['processing_time']:.3f}s")
            report_lines.append(f"Priority level: {best.get('priority', 'N/A')}")
            report_lines.append("")

            # Preprocessing details
            if 'preprocessing' in best:
                prep = best['preprocessing']
                report_lines.append("Preprocessing configuration:")
                for key, value in prep.items():
                    report_lines.append(f"  {key}: {value}")
                report_lines.append("")

            # Tesseract details
            if 'tesseract' in best:
                tess = best['tesseract']
                report_lines.append("Tesseract configuration:")
                for key, value in tess.items():
                    report_lines.append(f"  {key}: {value}")
                report_lines.append("")

            # Extracted text sample
            if 'extracted_lines' in best:
                lines = best['extracted_lines']
                report_lines.append("Extracted text (first 4 lines):")
                for i, line in enumerate(lines[:4], 1):
                    line_sim = best.get('line_similarities', [0])[i-1] if i <= len(best.get('line_similarities', [])) else 0
                    report_lines.append(f"  {i}: {line} (accuracy: {line_sim:.3f})")
                if len(lines) > 4:
                    report_lines.append(f"  ... and {len(lines) - 4} more lines")
                report_lines.append("")

        return "\n".join(report_lines)

    def generate_detailed_report(self, summary: Dict) -> str:
        """Generate a detailed text report of OCR results"""
        report_lines = []

        # Header
        report_lines.append("=" * 80)
        report_lines.append("MULTITHREADED OCR VALIDATION REPORT")
        report_lines.append("=" * 80)
        report_lines.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append(f"Reference file: {self.reference_file}")
        report_lines.append("")

        # Summary statistics
        report_lines.append("SUMMARY STATISTICS")
        report_lines.append("-" * 40)
        report_lines.append(f"Total images processed: {summary['total_images']}")
        report_lines.append(f"Total OCR attempts: {summary['total_attempts']}")
        report_lines.append(f"Perfect matches: {summary['perfect_matches']}")
        report_lines.append(f"High similarity matches (>80%): {summary['high_similarity_matches']}")
        report_lines.append(f"Processing time: {summary['processing_time']:.2f} seconds")
        report_lines.append(f"Attempts per second: {summary['attempts_per_second']:.2f}")
        report_lines.append("")

        # Reference lines
        report_lines.append("REFERENCE LINES (Ground Truth)")
        report_lines.append("-" * 40)
        for i, line in enumerate(self.reference_lines, 1):
            report_lines.append(f"  {i}: {line}")
        report_lines.append("")

        # Perfect matches
        perfect_results = [r for r in summary['results'] if r.get('is_perfect_match', False)]
        if perfect_results:
            report_lines.append("PERFECT MATCHES")
            report_lines.append("-" * 40)
            for result in perfect_results:
                report_lines.append(f"Image: {Path(result['image_path']).name}")
                report_lines.append(f"Config: {result['config_id']}")
                report_lines.append(f"Processing time: {result['processing_time']:.3f}s")
                report_lines.append("")

        # Best results per image
        report_lines.append("BEST RESULTS PER IMAGE")
        report_lines.append("-" * 40)

        # Group results by image
        image_results = {}
        for result in summary['results']:
            image_path = result['image_path']
            if image_path not in image_results:
                image_results[image_path] = []
            image_results[image_path].append(result)

        # Show best result for each image
        for image_path, results in image_results.items():
            best_result = max(results, key=lambda x: x.get('similarity_average', 0))

            report_lines.append(f"Image: {Path(image_path).name}")
            report_lines.append(f"Best config: {best_result['config_id']}")
            report_lines.append(f"Similarity: {best_result.get('similarity_average', 0):.3f}")
            report_lines.append(f"Perfect match: {'Yes' if best_result.get('is_perfect_match', False) else 'No'}")

            # Show extracted lines vs reference
            extracted_lines = best_result.get('extracted_lines', [])
            report_lines.append("Extracted lines:")
            for i, line in enumerate(extracted_lines[:4], 1):
                similarity = ""
                if i <= len(best_result.get('line_similarities', [])):
                    sim_score = best_result['line_similarities'][i-1]
                    similarity = f" (similarity: {sim_score:.3f})"
                report_lines.append(f"  {i}: {line}{similarity}")

            if len(extracted_lines) > 4:
                report_lines.append(f"  ... and {len(extracted_lines) - 4} more lines")

            report_lines.append("")

        return "\n".join(report_lines)

    def save_results(self, summary: Dict, output_prefix: str = "ocr_validation") -> Tuple[str, str]:
        """Save results to JSON and text files"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Save JSON results
        json_filename = f"{output_prefix}_{timestamp}.json"
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)

        # Save text report
        txt_filename = f"{output_prefix}_{timestamp}.txt"
        report = self.generate_detailed_report(summary)
        with open(txt_filename, 'w', encoding='utf-8') as f:
            f.write(report)

        logger.info(f"Results saved to {json_filename} and {txt_filename}")
        return json_filename, txt_filename

    def print_summary(self, summary: Dict):
        """Print a concise summary to console"""
        print("\n" + "=" * 60)
        print("OCR VALIDATION SUMMARY")
        print("=" * 60)

        print(f"📁 Images processed: {summary['total_images']}")
        print(f"🔍 Total OCR attempts: {summary['total_attempts']}")
        print(f"✅ Perfect matches: {summary['perfect_matches']}")
        print(f"📊 High similarity (>80%): {summary['high_similarity_matches']}")
        print(f"⏱️  Processing time: {summary['processing_time']:.2f}s")
        print(f"🚀 Speed: {summary['attempts_per_second']:.1f} attempts/second")

        # Show best results
        if summary['perfect_matches'] > 0:
            print(f"\n🎉 Found {summary['perfect_matches']} perfect matches!")
            perfect_results = [r for r in summary['results'] if r.get('is_perfect_match', False)]
            for result in perfect_results[:3]:  # Show first 3
                print(f"   • {Path(result['image_path']).name} with {result['config_id']}")

        # Show high similarity results if no perfect matches
        elif summary['high_similarity_matches'] > 0:
            print(f"\n📈 Best similarity results:")
            high_sim_results = [r for r in summary['results'] if r.get('similarity_average', 0) > 0.8]
            high_sim_results.sort(key=lambda x: x.get('similarity_average', 0), reverse=True)
            for result in high_sim_results[:3]:  # Show top 3
                print(f"   • {Path(result['image_path']).name}: {result.get('similarity_average', 0):.3f} "
                      f"({result['config_id']})")

        print("\n" + "=" * 60)


def main():
    """Enhanced main execution function with 95% accuracy threshold"""
    print("🚀 ENHANCED Multithreaded OCR Validator")
    print("High-performance OCR with 95% accuracy threshold and comprehensive testing")
    print("=" * 80)

    # Initialize enhanced validator
    try:
        validator = EnhancedMultithreadedOCRValidator(
            image_folder=".",
            reference_file="rsa_private_key.pem",
            max_workers=None,  # Use all CPU cores
            use_gpu=True       # Enable GPU acceleration if available
        )

        if not validator.reference_lines:
            print("❌ Could not load reference lines. Please check rsa_private_key.pem exists.")
            return

        print(f"📋 Reference validation target (first 4 lines):")
        for i, line in enumerate(validator.reference_lines[:4], 1):
            print(f"   {i}: {line[:50]}{'...' if len(line) > 50 else ''}")
        print()

        # Run enhanced OCR processing
        print("🚀 Starting ENHANCED multithreaded OCR processing...")
        print(f"🎯 Target: ≥{HIGH_CONFIDENCE_THRESHOLD:.0%} similarity on first 4 lines")
        print(f"💻 Using {validator.max_workers} CPU cores")
        print(f"⚙️  Testing {len(validator.ocr_configs)} configurations")
        print()

        summary = validator.run_enhanced_multithreaded_ocr()

        # Print enhanced summary
        validator.print_enhanced_summary(summary)

        # Save detailed results
        json_file, txt_file = validator.save_enhanced_results(summary)
        print(f"\n📄 Detailed results saved:")
        print(f"   • JSON: {json_file}")
        print(f"   • Report: {txt_file}")

        # Provide enhanced recommendations
        high_confidence = summary['high_confidence_matches']
        excellent = summary['excellent_matches']
        best_similarity = summary['best_similarity']

        print(f"\n{'='*60}")
        print("🎯 FINAL ASSESSMENT")
        print(f"{'='*60}")

        if high_confidence > 0:
            print(f"🏆 EXCELLENT! Found {high_confidence} configurations achieving ≥{HIGH_CONFIDENCE_THRESHOLD:.0%} accuracy!")
            print(f"✨ Best similarity: {best_similarity:.3f} ({best_similarity*100:.1f}%)")
            print("🎉 OCR is working at HIGH CONFIDENCE level for RSA key recognition.")

            if summary.get('best_overall_result'):
                best = summary['best_overall_result']
                print(f"\n🏅 BEST CONFIGURATION:")
                print(f"   • Config ID: {best['config_id']}")
                print(f"   • First 4 lines accuracy: {best.get('first_four_average', 0):.3f}")
                print(f"   • Processing time: {best['processing_time']:.2f}s")

        elif excellent > 0:
            print(f"⭐ VERY GOOD! Found {excellent} configurations achieving ≥90% accuracy.")
            print(f"📊 Best similarity: {best_similarity:.3f} ({best_similarity*100:.1f}%)")
            print("🔧 OCR is working well but may benefit from minor optimizations.")

        else:
            print(f"⚠️  No configurations achieved ≥90% accuracy.")
            print(f"📊 Best similarity: {best_similarity:.3f} ({best_similarity*100:.1f}%)")
            print("🔍 Recommendations:")
            print("   • Check image quality (resolution, contrast, lighting)")
            print("   • Verify text is clearly readable")
            print("   • Consider image preprocessing improvements")
            print("   • Check if reference file is accurate")

        # Performance summary
        print(f"\n⚡ PERFORMANCE SUMMARY:")
        print(f"   • Processing speed: {summary['configs_per_second']:.1f} configs/second")
        print(f"   • Time saved by optimization: {summary['time_saved_seconds']:.1f} seconds")
        print(f"   • GPU acceleration: {'Used' if summary['gpu_acceleration_used'] else 'Not available'}")
        print(f"   • Early termination: {'Yes' if summary['early_termination_triggered'] else 'No'}")

    except KeyboardInterrupt:
        print("\n🛑 Processing interrupted by user")
        logger.info("Processing interrupted by user")
    except Exception as e:
        logger.error(f"Error in enhanced main execution: {e}")
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
