#!/usr/bin/env python3
"""
Multithreaded OCR Validator Script
Processes multiple images concurrently and validates OCR results against reference text
"""

import os
import cv2
import pytesseract
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
import threading
from concurrent.futures import Thr<PERSON><PERSON><PERSON>Executor, as_completed
import time
from datetime import datetime
import json
import hashlib
from typing import List, Dict, Tuple, Optional
import difflib
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ocr_validator.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class MultithreadedOCRValidator:
    """
    Multithreaded OCR processor that validates results against reference text
    """
    
    def __init__(self, 
                 image_folder: str = ".", 
                 reference_file: str = "rsa_private_key.pem",
                 max_workers: int = None):
        """
        Initialize the OCR validator
        
        Args:
            image_folder: Directory containing images to process
            reference_file: Path to reference file for validation
            max_workers: Maximum number of worker threads (default: CPU count)
        """
        self.image_folder = Path(image_folder)
        self.reference_file = Path(reference_file)
        self.max_workers = max_workers or min(os.cpu_count(), 8)
        
        # Load reference lines
        self.reference_lines = self._load_reference_lines()
        
        # Supported image formats
        self.supported_formats = {'.jpg', '.jpeg', '.png', '.tiff', '.tif', '.bmp', '.gif', '.webp'}
        
        # Results storage
        self.results = []
        self.lock = threading.Lock()
        
        # OCR configurations to try
        self.ocr_configs = self._get_ocr_configurations()
        
        logger.info(f"Initialized OCR Validator with {self.max_workers} workers")
        logger.info(f"Reference lines loaded: {len(self.reference_lines)}")
    
    def _load_reference_lines(self) -> List[str]:
        """Load the first 4 lines from the reference file"""
        try:
            with open(self.reference_file, 'r', encoding='utf-8') as f:
                lines = [line.rstrip('\n\r') for line in f.readlines()[:4]]
            
            if len(lines) < 4:
                logger.warning(f"Reference file has only {len(lines)} lines, expected 4")
            
            logger.info("Reference lines:")
            for i, line in enumerate(lines, 1):
                logger.info(f"  {i}: {line}")
            
            return lines
            
        except Exception as e:
            logger.error(f"Failed to load reference file {self.reference_file}: {e}")
            return []
    
    def _get_ocr_configurations(self) -> List[Dict]:
        """Get different OCR preprocessing and Tesseract configurations to try"""
        configs = []
        
        # Different preprocessing methods
        preprocessing_methods = [
            {'name': 'original', 'scale': 1.0, 'denoise': False, 'threshold': None},
            {'name': 'scaled_2x', 'scale': 2.0, 'denoise': False, 'threshold': 'otsu'},
            {'name': 'scaled_3x', 'scale': 3.0, 'denoise': True, 'threshold': 'otsu'},
            {'name': 'denoised', 'scale': 2.0, 'denoise': True, 'threshold': 'adaptive'},
            {'name': 'enhanced', 'scale': 2.5, 'denoise': True, 'threshold': 'otsu', 'sharpen': True},
        ]
        
        # Different Tesseract configurations
        tesseract_configs = [
            {'name': 'default', 'psm': 6, 'oem': 3, 'whitelist': None},
            {'name': 'single_block', 'psm': 6, 'oem': 1, 'whitelist': 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=-'},
            {'name': 'single_line', 'psm': 7, 'oem': 3, 'whitelist': 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=-'},
            {'name': 'raw_line', 'psm': 8, 'oem': 3, 'whitelist': None},
            {'name': 'sparse_text', 'psm': 11, 'oem': 1, 'whitelist': 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=-'},
        ]
        
        # Create all combinations
        for prep in preprocessing_methods:
            for tess in tesseract_configs:
                configs.append({
                    'preprocessing': prep,
                    'tesseract': tess,
                    'config_id': f"{prep['name']}_{tess['name']}"
                })
        
        return configs
    
    def find_image_files(self) -> List[Path]:
        """Find all supported image files in the folder"""
        image_files = []
        
        try:
            for file_path in self.image_folder.iterdir():
                if file_path.is_file() and file_path.suffix.lower() in self.supported_formats:
                    image_files.append(file_path)
            
            logger.info(f"Found {len(image_files)} image files")
            for img_file in image_files:
                logger.info(f"  - {img_file.name}")
                
        except Exception as e:
            logger.error(f"Error scanning image folder: {e}")
        
        return image_files
    
    def preprocess_image(self, image_path: Path, config: Dict) -> Optional[np.ndarray]:
        """Apply preprocessing to image based on configuration"""
        try:
            # Load image
            img = cv2.imread(str(image_path))
            if img is None:
                logger.error(f"Could not load image: {image_path}")
                return None
            
            # Convert to grayscale
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            # Apply scaling
            if config.get('scale', 1.0) != 1.0:
                scale = config['scale']
                height, width = gray.shape
                new_width = int(width * scale)
                new_height = int(height * scale)
                gray = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
            
            # Apply denoising
            if config.get('denoise', False):
                gray = cv2.fastNlMeansDenoising(gray)
            
            # Apply thresholding
            if config.get('threshold') == 'otsu':
                _, gray = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            elif config.get('threshold') == 'adaptive':
                gray = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                           cv2.THRESH_BINARY, 11, 2)
            
            # Apply sharpening
            if config.get('sharpen', False):
                kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
                gray = cv2.filter2D(gray, -1, kernel)
            
            return gray
            
        except Exception as e:
            logger.error(f"Error preprocessing image {image_path}: {e}")
            return None
    
    def extract_text_with_tesseract(self, image: np.ndarray, config: Dict) -> str:
        """Extract text using Tesseract with specific configuration"""
        try:
            # Build Tesseract config string
            psm = config.get('psm', 6)
            oem = config.get('oem', 3)
            whitelist = config.get('whitelist')
            
            tess_config = f'--oem {oem} --psm {psm}'
            if whitelist:
                tess_config += f' -c tessedit_char_whitelist={whitelist}'
            
            # Convert numpy array to PIL Image for Tesseract
            pil_image = Image.fromarray(image)
            
            # Extract text
            text = pytesseract.image_to_string(pil_image, config=tess_config)
            return text.strip()
            
        except Exception as e:
            logger.error(f"Error extracting text with Tesseract: {e}")
            return ""
    
    def calculate_similarity(self, ocr_lines: List[str]) -> Dict[str, float]:
        """Calculate similarity metrics between OCR results and reference lines"""
        if not self.reference_lines or not ocr_lines:
            return {'average': 0.0, 'line_similarities': []}
        
        line_similarities = []
        
        # Compare first 4 lines (or as many as available)
        max_lines = min(len(self.reference_lines), len(ocr_lines), 4)
        
        for i in range(max_lines):
            ref_line = self.reference_lines[i]
            ocr_line = ocr_lines[i] if i < len(ocr_lines) else ""
            
            # Calculate character-level similarity
            if len(ref_line) == 0 and len(ocr_line) == 0:
                similarity = 1.0
            elif len(ref_line) == 0 or len(ocr_line) == 0:
                similarity = 0.0
            else:
                # Use difflib for sequence matching
                matcher = difflib.SequenceMatcher(None, ref_line, ocr_line)
                similarity = matcher.ratio()
            
            line_similarities.append(similarity)
        
        # Calculate average similarity
        average_similarity = sum(line_similarities) / len(line_similarities) if line_similarities else 0.0
        
        return {
            'average': average_similarity,
            'line_similarities': line_similarities
        }
    
    def is_perfect_match(self, ocr_lines: List[str]) -> bool:
        """Check if OCR results perfectly match the first 4 reference lines"""
        if len(ocr_lines) < 4 or len(self.reference_lines) < 4:
            return False

        return all(ocr_lines[i] == self.reference_lines[i] for i in range(4))

    def process_single_image_config(self, image_path: Path, config: Dict) -> Dict:
        """Process a single image with a specific OCR configuration"""
        start_time = time.time()

        try:
            # Preprocess image
            processed_image = self.preprocess_image(image_path, config['preprocessing'])
            if processed_image is None:
                return {
                    'image_path': str(image_path),
                    'config_id': config['config_id'],
                    'error': 'Failed to preprocess image',
                    'processing_time': time.time() - start_time
                }

            # Extract text
            raw_text = self.extract_text_with_tesseract(processed_image, config['tesseract'])

            # Parse lines
            lines = [line.strip() for line in raw_text.split('\n') if line.strip()]

            # Calculate metrics
            similarity_metrics = self.calculate_similarity(lines)
            is_perfect = self.is_perfect_match(lines)

            # Create result
            result = {
                'image_path': str(image_path),
                'config_id': config['config_id'],
                'preprocessing': config['preprocessing'],
                'tesseract': config['tesseract'],
                'raw_text': raw_text,
                'extracted_lines': lines,
                'total_lines': len(lines),
                'is_perfect_match': is_perfect,
                'similarity_average': similarity_metrics['average'],
                'line_similarities': similarity_metrics['line_similarities'],
                'processing_time': time.time() - start_time,
                'timestamp': datetime.now().isoformat()
            }

            return result

        except Exception as e:
            logger.error(f"Error processing {image_path} with config {config['config_id']}: {e}")
            return {
                'image_path': str(image_path),
                'config_id': config['config_id'],
                'error': str(e),
                'processing_time': time.time() - start_time
            }

    def process_single_image(self, image_path: Path) -> List[Dict]:
        """Process a single image with all OCR configurations"""
        logger.info(f"Processing image: {image_path.name}")

        image_results = []

        # Try each OCR configuration
        for config in self.ocr_configs:
            result = self.process_single_image_config(image_path, config)
            image_results.append(result)

            # Log if we found a perfect match
            if result.get('is_perfect_match', False):
                logger.info(f"🎉 Perfect match found for {image_path.name} with config {config['config_id']}")

        # Thread-safe result storage
        with self.lock:
            self.results.extend(image_results)

        return image_results

    def run_multithreaded_ocr(self) -> Dict:
        """Run OCR on all images using multithreading"""
        start_time = time.time()

        # Find all image files
        image_files = self.find_image_files()

        if not image_files:
            logger.warning("No image files found to process")
            return {
                'total_images': 0,
                'total_attempts': 0,
                'perfect_matches': 0,
                'processing_time': 0,
                'results': []
            }

        logger.info(f"Starting multithreaded OCR processing with {self.max_workers} workers")
        logger.info(f"Processing {len(image_files)} images with {len(self.ocr_configs)} configurations each")

        # Process images in parallel
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all image processing tasks
            future_to_image = {
                executor.submit(self.process_single_image, image_path): image_path
                for image_path in image_files
            }

            # Collect results as they complete
            completed_images = 0
            for future in as_completed(future_to_image):
                image_path = future_to_image[future]
                try:
                    image_results = future.result()
                    completed_images += 1

                    # Find best result for this image
                    best_result = max(image_results, key=lambda x: x.get('similarity_average', 0))
                    logger.info(f"Completed {completed_images}/{len(image_files)}: {image_path.name} "
                              f"(Best similarity: {best_result.get('similarity_average', 0):.3f})")

                except Exception as e:
                    logger.error(f"Error processing {image_path}: {e}")

        # Calculate summary statistics
        total_time = time.time() - start_time
        perfect_matches = sum(1 for result in self.results if result.get('is_perfect_match', False))
        high_similarity_matches = sum(1 for result in self.results if result.get('similarity_average', 0) > 0.8)

        summary = {
            'total_images': len(image_files),
            'total_attempts': len(self.results),
            'perfect_matches': perfect_matches,
            'high_similarity_matches': high_similarity_matches,
            'processing_time': total_time,
            'attempts_per_second': len(self.results) / total_time if total_time > 0 else 0,
            'results': self.results
        }

        logger.info(f"OCR processing completed in {total_time:.2f} seconds")
        logger.info(f"Perfect matches: {perfect_matches}")
        logger.info(f"High similarity matches (>80%): {high_similarity_matches}")

        return summary

    def generate_detailed_report(self, summary: Dict) -> str:
        """Generate a detailed text report of OCR results"""
        report_lines = []

        # Header
        report_lines.append("=" * 80)
        report_lines.append("MULTITHREADED OCR VALIDATION REPORT")
        report_lines.append("=" * 80)
        report_lines.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append(f"Reference file: {self.reference_file}")
        report_lines.append("")

        # Summary statistics
        report_lines.append("SUMMARY STATISTICS")
        report_lines.append("-" * 40)
        report_lines.append(f"Total images processed: {summary['total_images']}")
        report_lines.append(f"Total OCR attempts: {summary['total_attempts']}")
        report_lines.append(f"Perfect matches: {summary['perfect_matches']}")
        report_lines.append(f"High similarity matches (>80%): {summary['high_similarity_matches']}")
        report_lines.append(f"Processing time: {summary['processing_time']:.2f} seconds")
        report_lines.append(f"Attempts per second: {summary['attempts_per_second']:.2f}")
        report_lines.append("")

        # Reference lines
        report_lines.append("REFERENCE LINES (Ground Truth)")
        report_lines.append("-" * 40)
        for i, line in enumerate(self.reference_lines, 1):
            report_lines.append(f"  {i}: {line}")
        report_lines.append("")

        # Perfect matches
        perfect_results = [r for r in summary['results'] if r.get('is_perfect_match', False)]
        if perfect_results:
            report_lines.append("PERFECT MATCHES")
            report_lines.append("-" * 40)
            for result in perfect_results:
                report_lines.append(f"Image: {Path(result['image_path']).name}")
                report_lines.append(f"Config: {result['config_id']}")
                report_lines.append(f"Processing time: {result['processing_time']:.3f}s")
                report_lines.append("")

        # Best results per image
        report_lines.append("BEST RESULTS PER IMAGE")
        report_lines.append("-" * 40)

        # Group results by image
        image_results = {}
        for result in summary['results']:
            image_path = result['image_path']
            if image_path not in image_results:
                image_results[image_path] = []
            image_results[image_path].append(result)

        # Show best result for each image
        for image_path, results in image_results.items():
            best_result = max(results, key=lambda x: x.get('similarity_average', 0))

            report_lines.append(f"Image: {Path(image_path).name}")
            report_lines.append(f"Best config: {best_result['config_id']}")
            report_lines.append(f"Similarity: {best_result.get('similarity_average', 0):.3f}")
            report_lines.append(f"Perfect match: {'Yes' if best_result.get('is_perfect_match', False) else 'No'}")

            # Show extracted lines vs reference
            extracted_lines = best_result.get('extracted_lines', [])
            report_lines.append("Extracted lines:")
            for i, line in enumerate(extracted_lines[:4], 1):
                similarity = ""
                if i <= len(best_result.get('line_similarities', [])):
                    sim_score = best_result['line_similarities'][i-1]
                    similarity = f" (similarity: {sim_score:.3f})"
                report_lines.append(f"  {i}: {line}{similarity}")

            if len(extracted_lines) > 4:
                report_lines.append(f"  ... and {len(extracted_lines) - 4} more lines")

            report_lines.append("")

        return "\n".join(report_lines)

    def save_results(self, summary: Dict, output_prefix: str = "ocr_validation") -> Tuple[str, str]:
        """Save results to JSON and text files"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Save JSON results
        json_filename = f"{output_prefix}_{timestamp}.json"
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)

        # Save text report
        txt_filename = f"{output_prefix}_{timestamp}.txt"
        report = self.generate_detailed_report(summary)
        with open(txt_filename, 'w', encoding='utf-8') as f:
            f.write(report)

        logger.info(f"Results saved to {json_filename} and {txt_filename}")
        return json_filename, txt_filename

    def print_summary(self, summary: Dict):
        """Print a concise summary to console"""
        print("\n" + "=" * 60)
        print("OCR VALIDATION SUMMARY")
        print("=" * 60)

        print(f"📁 Images processed: {summary['total_images']}")
        print(f"🔍 Total OCR attempts: {summary['total_attempts']}")
        print(f"✅ Perfect matches: {summary['perfect_matches']}")
        print(f"📊 High similarity (>80%): {summary['high_similarity_matches']}")
        print(f"⏱️  Processing time: {summary['processing_time']:.2f}s")
        print(f"🚀 Speed: {summary['attempts_per_second']:.1f} attempts/second")

        # Show best results
        if summary['perfect_matches'] > 0:
            print(f"\n🎉 Found {summary['perfect_matches']} perfect matches!")
            perfect_results = [r for r in summary['results'] if r.get('is_perfect_match', False)]
            for result in perfect_results[:3]:  # Show first 3
                print(f"   • {Path(result['image_path']).name} with {result['config_id']}")

        # Show high similarity results if no perfect matches
        elif summary['high_similarity_matches'] > 0:
            print(f"\n📈 Best similarity results:")
            high_sim_results = [r for r in summary['results'] if r.get('similarity_average', 0) > 0.8]
            high_sim_results.sort(key=lambda x: x.get('similarity_average', 0), reverse=True)
            for result in high_sim_results[:3]:  # Show top 3
                print(f"   • {Path(result['image_path']).name}: {result.get('similarity_average', 0):.3f} "
                      f"({result['config_id']})")

        print("\n" + "=" * 60)


def main():
    """Main execution function"""
    print("🔍 Multithreaded OCR Validator")
    print("Processes images and validates OCR results against reference text")
    print()

    # Initialize validator
    try:
        validator = MultithreadedOCRValidator(
            image_folder=".",
            reference_file="rsa_private_key.pem",
            max_workers=None  # Use default (CPU count)
        )

        if not validator.reference_lines:
            print("❌ Could not load reference lines. Please check rsa_private_key.pem exists.")
            return

        # Run OCR processing
        print("🚀 Starting multithreaded OCR processing...")
        summary = validator.run_multithreaded_ocr()

        # Print summary
        validator.print_summary(summary)

        # Save detailed results
        json_file, txt_file = validator.save_results(summary)
        print(f"\n📄 Detailed results saved:")
        print(f"   • JSON: {json_file}")
        print(f"   • Report: {txt_file}")

        # Provide recommendations
        if summary['perfect_matches'] > 0:
            print(f"\n✨ Excellent! Found {summary['perfect_matches']} perfect OCR matches.")
            print("The OCR is working correctly for these configurations.")
        elif summary['high_similarity_matches'] > 0:
            print(f"\n🔧 Found {summary['high_similarity_matches']} high-similarity matches.")
            print("OCR is mostly working but may need fine-tuning or better image quality.")
        else:
            print("\n⚠️  No high-quality OCR results found.")
            print("Consider:")
            print("   • Improving image quality (resolution, contrast, lighting)")
            print("   • Trying different image preprocessing")
            print("   • Checking if text is clearly readable in the images")

    except Exception as e:
        logger.error(f"Error in main execution: {e}")
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
