#!/usr/bin/env python3
"""
Focused test for multithreading and GPU acceleration fixes
"""

from multithreaded_ocr_validator import EnhancedMultithreadedOCRValidator, SystemProfiler
import time
import threading
import psutil
import os

def test_gpu_detection():
    """Test enhanced GPU detection"""
    print("🎮 Testing Enhanced GPU Detection...")
    print("=" * 60)
    
    # Get system info
    system_info = SystemProfiler.get_system_info()
    
    print(f"GPU Model: {system_info.gpu_model}")
    print(f"VRAM: {system_info.gpu_vram_gb:.1f}GB")
    print(f"CUDA Available: {system_info.cuda_available}")
    print(f"CUDA Version: {system_info.cuda_version}")
    print(f"OpenCV CUDA: {system_info.opencv_cuda_support}")
    print(f"OpenCV OpenCL: {system_info.opencv_opencl_support}")
    
    return system_info

def test_multithreading_verification():
    """Test actual multithreading with thread monitoring"""
    print("\n🧵 Testing Multithreading Verification...")
    print("=" * 60)
    
    # Create validator with limited configs for testing
    validator = EnhancedMultithreadedOCRValidator(
        image_folder=".",
        reference_file="rsa_private_key.pem",
        max_workers=4,
        use_gpu=True,
        use_processes=False  # Test ThreadPoolExecutor first
    )
    
    # Create minimal test configurations
    test_configs = []
    for i in range(12):  # 12 configs to ensure multiple threads are used
        config = {
            'preprocessing': {
                'name': f'test_config_{i}',
                'scale': 1.5 + (i * 0.1),
                'threshold': 'otsu',
                'denoise': False
            },
            'tesseract': {
                'psm': 6 + (i % 3),
                'oem': 1 + (i % 2),
                'whitelist': None
            },
            'priority': 1,
            'config_id': f'multithread_test_{i}'
        }
        test_configs.append(config)
    
    validator.ocr_configs = test_configs
    
    print(f"🔧 Testing with {len(test_configs)} configurations")
    print(f"💻 Using {validator.max_workers} threads")
    print(f"📊 Monitoring CPU usage and thread activity...")
    
    # Monitor CPU usage before
    cpu_before = psutil.cpu_percent(interval=1)
    
    # Run processing
    start_time = time.time()
    summary = validator.run_enhanced_multithreaded_ocr()
    end_time = time.time()
    
    # Monitor CPU usage after
    cpu_after = psutil.cpu_percent(interval=1)
    
    print(f"\n📊 Multithreading Test Results:")
    print(f"   Processing time: {end_time - start_time:.2f}s")
    print(f"   Configurations processed: {summary['processed_configurations']}")
    print(f"   CPU usage before: {cpu_before:.1f}%")
    print(f"   CPU usage during: {cpu_after:.1f}%")
    print(f"   Processing rate: {summary['configs_per_second']:.1f} configs/sec")
    
    # Verify multithreading worked
    if summary['configs_per_second'] > 2.0:  # Should be faster with multiple threads
        print(f"   ✅ Multithreading appears to be working (good speed)")
    else:
        print(f"   ⚠️ Multithreading may not be working optimally (slow speed)")
    
    return summary

def test_process_pool():
    """Test ProcessPoolExecutor vs ThreadPoolExecutor"""
    print("\n🔄 Testing ProcessPoolExecutor...")
    print("=" * 60)
    
    # Test with ProcessPoolExecutor
    validator_process = EnhancedMultithreadedOCRValidator(
        image_folder=".",
        reference_file="rsa_private_key.pem",
        max_workers=4,
        use_gpu=True,
        use_processes=True  # Use ProcessPoolExecutor
    )
    
    # Same test configs
    test_configs = []
    for i in range(8):  # Fewer configs for process test
        config = {
            'preprocessing': {
                'name': f'process_test_{i}',
                'scale': 2.0,
                'threshold': 'otsu',
                'denoise': False
            },
            'tesseract': {
                'psm': 6,
                'oem': 3,
                'whitelist': None
            },
            'priority': 1,
            'config_id': f'process_test_{i}'
        }
        test_configs.append(config)
    
    validator_process.ocr_configs = test_configs
    
    print(f"🔧 Testing ProcessPoolExecutor with {len(test_configs)} configurations")
    
    start_time = time.time()
    summary_process = validator_process.run_enhanced_multithreaded_ocr()
    process_time = time.time() - start_time
    
    print(f"\n📊 ProcessPoolExecutor Results:")
    print(f"   Processing time: {process_time:.2f}s")
    print(f"   Configurations processed: {summary_process['processed_configurations']}")
    print(f"   Processing rate: {summary_process['configs_per_second']:.1f} configs/sec")
    
    return summary_process

def test_gpu_acceleration():
    """Test GPU acceleration functionality"""
    print("\n🎮 Testing GPU Acceleration...")
    print("=" * 60)
    
    validator = EnhancedMultithreadedOCRValidator(
        image_folder=".",
        reference_file="rsa_private_key.pem",
        max_workers=2,
        use_gpu=True,
        use_processes=False
    )
    
    # Create GPU-specific test configs
    gpu_configs = [
        {
            'preprocessing': {
                'name': 'gpu_test_fastNl',
                'scale': 3.0,
                'threshold': 'otsu',
                'denoise': 'fastNl'  # Should trigger GPU acceleration
            },
            'tesseract': {'psm': 6, 'oem': 3, 'whitelist': None},
            'priority': 1,
            'config_id': 'gpu_fastNl_test'
        },
        {
            'preprocessing': {
                'name': 'gpu_test_otsu',
                'scale': 2.5,
                'threshold': 'otsu',  # Should trigger GPU acceleration
                'denoise': False
            },
            'tesseract': {'psm': 6, 'oem': 3, 'whitelist': None},
            'priority': 1,
            'config_id': 'gpu_otsu_test'
        }
    ]
    
    validator.ocr_configs = gpu_configs
    
    print(f"🔧 Testing GPU acceleration with {len(gpu_configs)} configurations")
    print(f"🎮 GPU Available: {validator.gpu_available}")
    print(f"🚀 GPU Model: {validator.system_info.gpu_model}")
    
    if validator.gpu_available:
        print(f"✅ GPU should be used for fastNl denoising and Otsu thresholding")
    else:
        print(f"⚠️ GPU not available, will test CPU fallback")
    
    start_time = time.time()
    summary_gpu = validator.run_enhanced_multithreaded_ocr()
    gpu_time = time.time() - start_time
    
    print(f"\n📊 GPU Test Results:")
    print(f"   Processing time: {gpu_time:.2f}s")
    print(f"   GPU acceleration used: {summary_gpu['gpu_acceleration_used']}")
    print(f"   Best similarity: {summary_gpu['best_similarity']:.3f}")
    
    return summary_gpu

def main():
    """Run focused multithreading and GPU tests"""
    print("🚀 FOCUSED MULTITHREADING & GPU TESTING")
    print("=" * 80)
    
    try:
        # Test 1: GPU Detection
        system_info = test_gpu_detection()
        
        # Test 2: Multithreading Verification
        thread_summary = test_multithreading_verification()
        
        # Test 3: Process Pool Comparison
        process_summary = test_process_pool()
        
        # Test 4: GPU Acceleration
        gpu_summary = test_gpu_acceleration()
        
        # Final Analysis
        print(f"\n{'='*80}")
        print(f"🎯 FINAL ANALYSIS")
        print(f"{'='*80}")
        
        # Multithreading Analysis
        thread_rate = thread_summary['configs_per_second']
        process_rate = process_summary['configs_per_second']
        
        print(f"🧵 ThreadPoolExecutor: {thread_rate:.1f} configs/sec")
        print(f"🔄 ProcessPoolExecutor: {process_rate:.1f} configs/sec")
        
        if thread_rate > 2.0:
            print(f"✅ ThreadPoolExecutor is working properly")
        else:
            print(f"❌ ThreadPoolExecutor may have issues")
        
        if process_rate > 1.0:
            print(f"✅ ProcessPoolExecutor is working properly")
        else:
            print(f"❌ ProcessPoolExecutor may have issues")
        
        # GPU Analysis
        print(f"\n🎮 GPU Analysis:")
        print(f"   GPU Available: {system_info.cuda_available}")
        print(f"   GPU Model: {system_info.gpu_model}")
        print(f"   GPU Used in Test: {gpu_summary['gpu_acceleration_used']}")
        
        if system_info.cuda_available and gpu_summary['gpu_acceleration_used']:
            print(f"✅ GPU acceleration is working")
        elif system_info.cuda_available:
            print(f"⚠️ GPU available but not used - check implementation")
        else:
            print(f"ℹ️ GPU not available on this system")
        
        print(f"\n🎉 Testing completed!")
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
