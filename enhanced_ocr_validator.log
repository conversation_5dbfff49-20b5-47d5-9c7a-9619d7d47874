2025-06-10 22:58:48,937 - INFO - 💻 GPU acceleration not available, using CPU only
2025-06-10 22:58:48,937 - INFO - Reference lines:
2025-06-10 22:58:48,937 - INFO -   1: -----BEGIN RSA PRIVATE KEY-----
2025-06-10 22:58:48,937 - INFO -   2: MIIJKQIBAAKCAgEApXLc+tmBmDZl6NxuM+IohxYTOph3G/Pt/plQH3KsowSzM62E
2025-06-10 22:58:48,937 - INFO -   3: TSrkn9n3+qjLYU3ThgPPWCBBKdlf57c27eja37YWq7tlro6XD97YO4PtXMbiB6vn
2025-06-10 22:58:48,937 - INFO -   4: amPr13GFnuxQ1FtMDjGp91q234B2p7/QD4AJ/oz1TzAwyibAe80qWiuMqH3R6Kr0
2025-06-10 22:58:48,944 - INFO - 📋 Generated 5790 comprehensive OCR configurations
2025-06-10 22:58:48,944 - INFO - 🎯 Priority configurations: 10
2025-06-10 22:58:48,945 - INFO - 🔍 Standard configurations: 5600
2025-06-10 22:58:48,945 - INFO - 🧪 Extended configurations: 180
2025-06-10 22:58:48,945 - INFO - 🚀 Enhanced OCR Validator initialized
2025-06-10 22:58:48,945 - INFO - 💻 Using 32 CPU cores for maximum performance
2025-06-10 22:58:48,945 - INFO - 🎯 High confidence threshold: 95.0%
2025-06-10 22:58:48,945 - INFO - 📊 Total configurations to test: 5790
2025-06-10 22:58:48,945 - INFO - 🖥️  GPU acceleration: Not available
2025-06-10 22:58:48,945 - INFO - 📝 Reference lines loaded: 4
2025-06-10 22:58:48,945 - INFO - Found 1 image files
2025-06-10 22:58:48,945 - INFO -   - WhatsApp Image 2025-06-06 at 09.57.47.jpeg
2025-06-10 22:58:48,945 - INFO - 🚀 Starting ENHANCED multithreaded OCR processing
2025-06-10 22:58:48,945 - INFO - 💻 Using ALL 32 CPU cores for maximum performance
2025-06-10 22:58:48,945 - INFO - 🖼️  Processing 1 images
2025-06-10 22:58:48,945 - INFO - ⚙️  Testing up to 5790 configurations per image
2025-06-10 22:58:48,945 - INFO - 🎯 High confidence threshold: 95.0%
2025-06-10 22:58:48,945 - INFO - 🔬 Ground truth accuracy assumption: 98.0%
2025-06-10 22:58:48,946 - INFO - 🖼️  Processing image: WhatsApp Image 2025-06-06 at 09.57.47.jpeg
2025-06-10 23:00:23,871 - INFO - 💻 GPU acceleration not available, using CPU only
2025-06-10 23:00:23,871 - INFO - Reference lines:
2025-06-10 23:00:23,871 - INFO -   1: -----BEGIN RSA PRIVATE KEY-----
2025-06-10 23:00:23,871 - INFO -   2: MIIJKQIBAAKCAgEApXLc+tmBmDZl6NxuM+IohxYTOph3G/Pt/plQH3KsowSzM62E
2025-06-10 23:00:23,871 - INFO -   3: TSrkn9n3+qjLYU3ThgPPWCBBKdlf57c27eja37YWq7tlro6XD97YO4PtXMbiB6vn
2025-06-10 23:00:23,871 - INFO -   4: amPr13GFnuxQ1FtMDjGp91q234B2p7/QD4AJ/oz1TzAwyibAe80qWiuMqH3R6Kr0
2025-06-10 23:00:23,878 - INFO - 📋 Generated 5790 comprehensive OCR configurations
2025-06-10 23:00:23,878 - INFO - 🎯 Priority configurations: 10
2025-06-10 23:00:23,878 - INFO - 🔍 Standard configurations: 5600
2025-06-10 23:00:23,878 - INFO - 🧪 Extended configurations: 180
2025-06-10 23:00:23,878 - INFO - 🚀 Enhanced OCR Validator initialized
2025-06-10 23:00:23,878 - INFO - 💻 Using 32 CPU cores for maximum performance
2025-06-10 23:00:23,878 - INFO - 🎯 High confidence threshold: 95.0%
2025-06-10 23:00:23,878 - INFO - 📊 Total configurations to test: 5790
2025-06-10 23:00:23,878 - INFO - 🖥️  GPU acceleration: Not available
2025-06-10 23:00:23,878 - INFO - 📝 Reference lines loaded: 4
2025-06-10 23:00:23,878 - INFO - Found 1 image files
2025-06-10 23:00:23,878 - INFO -   - WhatsApp Image 2025-06-06 at 09.57.47.jpeg
2025-06-10 23:00:23,878 - INFO - 🚀 Starting ENHANCED multithreaded OCR processing
2025-06-10 23:00:23,878 - INFO - 💻 Using ALL 32 CPU cores for maximum performance
2025-06-10 23:00:23,878 - INFO - 🖼️  Processing 1 images
2025-06-10 23:00:23,878 - INFO - ⚙️  Testing up to 5790 configurations per image
2025-06-10 23:00:23,878 - INFO - 🎯 High confidence threshold: 95.0%
2025-06-10 23:00:23,878 - INFO - 🔬 Ground truth accuracy assumption: 98.0%
2025-06-10 23:00:23,879 - INFO - 🖼️  Processing image: WhatsApp Image 2025-06-06 at 09.57.47.jpeg
2025-06-10 23:00:27,227 - INFO - 🛑 Received interrupt signal, finishing current tasks...
2025-06-10 23:00:27,229 - ERROR - Error extracting text with Tesseract: (-2, '')
2025-06-10 23:00:30,081 - INFO - 🛑 Received interrupt signal, finishing current tasks...
2025-06-10 23:00:30,083 - ERROR - Error extracting text with Tesseract: (-2, '')
2025-06-10 23:00:30,257 - INFO - 🛑 Received interrupt signal, finishing current tasks...
2025-06-10 23:00:30,408 - INFO - 🛑 Received interrupt signal, finishing current tasks...
2025-06-10 23:00:30,553 - INFO - 🛑 Received interrupt signal, finishing current tasks...
2025-06-10 23:00:30,693 - INFO - 🛑 Received interrupt signal, finishing current tasks...
2025-06-10 23:00:30,843 - INFO - 🛑 Received interrupt signal, finishing current tasks...
2025-06-10 23:00:30,845 - ERROR - Error extracting text with Tesseract: (-2, '')
2025-06-10 23:00:31,015 - INFO - 🛑 Received interrupt signal, finishing current tasks...
2025-06-10 23:00:31,167 - INFO - 🛑 Received interrupt signal, finishing current tasks...
2025-06-10 23:00:31,315 - INFO - 🛑 Received interrupt signal, finishing current tasks...
2025-06-10 23:00:31,473 - INFO - 🛑 Received interrupt signal, finishing current tasks...
2025-06-10 23:00:31,626 - INFO - 🛑 Received interrupt signal, finishing current tasks...
2025-06-10 23:00:31,628 - ERROR - Error extracting text with Tesseract: (-2, '')
2025-06-10 23:00:32,281 - INFO - 🛑 Received interrupt signal, finishing current tasks...
2025-06-10 23:00:32,281 - INFO - 🛑 Received interrupt signal, finishing current tasks...
2025-06-10 23:00:32,283 - ERROR - Error extracting text with Tesseract: (-2, '')
2025-06-10 23:00:32,307 - INFO - 🛑 Received interrupt signal, finishing current tasks...
2025-06-10 23:00:32,347 - INFO - 🛑 Received interrupt signal, finishing current tasks...
2025-06-10 23:00:32,348 - ERROR - Error extracting text with Tesseract: (-2, '')
2025-06-10 23:00:32,387 - INFO - 🛑 Received interrupt signal, finishing current tasks...
2025-06-10 23:00:36,918 - INFO - 🛑 Received interrupt signal, finishing current tasks...
2025-06-10 23:00:36,919 - ERROR - Error extracting text with Tesseract: (-2, '')
2025-06-10 23:00:37,224 - INFO - 🛑 Received interrupt signal, finishing current tasks...
2025-06-10 23:00:37,226 - ERROR - Error extracting text with Tesseract: (-2, '')
2025-06-10 23:00:37,228 - INFO - ✅ Completed WhatsApp Image 2025-06-06 at 09.57.47.jpeg: 10 results, 0 high confidence
2025-06-10 23:00:37,228 - INFO - ✅ Completed 1/1: WhatsApp Image 2025-06-06 at 09.57.47.jpeg
2025-06-10 23:00:37,228 - INFO -    📊 Best first-4-lines similarity: 0.962
2025-06-10 23:00:37,228 - INFO -    ⚡ Processing rate: 0.6 configs/sec
2025-06-10 23:00:37,228 - INFO -    🎯 High confidence results so far: 0
2025-06-10 23:00:37,228 - INFO -    🏆 HIGH CONFIDENCE ACHIEVED! (0.962)
2025-06-10 23:00:37,228 - INFO - 🎉 ENHANCED OCR PROCESSING COMPLETED!
2025-06-10 23:00:37,228 - INFO - ⏱️  Total time: 13.35 seconds
2025-06-10 23:00:37,228 - INFO - ⚡ Processing speed: 0.6 configurations/second
2025-06-10 23:00:37,229 - INFO - 🎯 High confidence matches (≥95%): 0
2025-06-10 23:00:37,229 - INFO - ⭐ Excellent matches (≥90%): 2
2025-06-10 23:00:37,229 - INFO - 👍 Good matches (≥80%): 2
2025-06-10 23:00:37,229 - INFO - 🏆 Best similarity achieved: 0.000
2025-06-10 23:00:37,229 - INFO - ⚡ Time saved by early termination: 9645.4 seconds
2025-06-10 23:00:37,229 - INFO - 📄 Enhanced results saved to enhanced_ocr_validation_20250610_230037.json and enhanced_ocr_validation_20250610_230037.txt
2025-06-10 23:00:46,081 - INFO - 💻 GPU acceleration not available, using CPU only
2025-06-10 23:00:46,081 - INFO - Reference lines:
2025-06-10 23:00:46,081 - INFO -   1: -----BEGIN RSA PRIVATE KEY-----
2025-06-10 23:00:46,081 - INFO -   2: MIIJKQIBAAKCAgEApXLc+tmBmDZl6NxuM+IohxYTOph3G/Pt/plQH3KsowSzM62E
2025-06-10 23:00:46,081 - INFO -   3: TSrkn9n3+qjLYU3ThgPPWCBBKdlf57c27eja37YWq7tlro6XD97YO4PtXMbiB6vn
2025-06-10 23:00:46,081 - INFO -   4: amPr13GFnuxQ1FtMDjGp91q234B2p7/QD4AJ/oz1TzAwyibAe80qWiuMqH3R6Kr0
2025-06-10 23:00:46,089 - INFO - 📋 Generated 5790 comprehensive OCR configurations
2025-06-10 23:00:46,089 - INFO - 🎯 Priority configurations: 10
2025-06-10 23:00:46,089 - INFO - 🔍 Standard configurations: 5600
2025-06-10 23:00:46,089 - INFO - 🧪 Extended configurations: 180
2025-06-10 23:00:46,089 - INFO - 🚀 Enhanced OCR Validator initialized
2025-06-10 23:00:46,090 - INFO - 💻 Using 4 CPU cores for maximum performance
2025-06-10 23:00:46,090 - INFO - 🎯 High confidence threshold: 95.0%
2025-06-10 23:00:46,090 - INFO - 📊 Total configurations to test: 5790
2025-06-10 23:00:46,090 - INFO - 🖥️  GPU acceleration: Not available
2025-06-10 23:00:46,090 - INFO - 📝 Reference lines loaded: 4
2025-06-10 23:00:46,090 - INFO - Found 1 image files
2025-06-10 23:00:46,090 - INFO -   - WhatsApp Image 2025-06-06 at 09.57.47.jpeg
2025-06-10 23:00:46,090 - INFO - 🚀 Starting ENHANCED multithreaded OCR processing
2025-06-10 23:00:46,090 - INFO - 💻 Using ALL 4 CPU cores for maximum performance
2025-06-10 23:00:46,090 - INFO - 🖼️  Processing 1 images
2025-06-10 23:00:46,090 - INFO - ⚙️  Testing up to 5 configurations per image
2025-06-10 23:00:46,090 - INFO - 🎯 High confidence threshold: 95.0%
2025-06-10 23:00:46,090 - INFO - 🔬 Ground truth accuracy assumption: 98.0%
2025-06-10 23:00:46,090 - INFO - 🖼️  Processing image: WhatsApp Image 2025-06-06 at 09.57.47.jpeg
2025-06-10 23:00:58,338 - INFO - ✅ Completed WhatsApp Image 2025-06-06 at 09.57.47.jpeg: 5 results, 0 high confidence
2025-06-10 23:00:58,338 - INFO - ✅ Completed 1/1: WhatsApp Image 2025-06-06 at 09.57.47.jpeg
2025-06-10 23:00:58,338 - INFO -    📊 Best first-4-lines similarity: 0.962
2025-06-10 23:00:58,338 - INFO -    ⚡ Processing rate: 0.1 configs/sec
2025-06-10 23:00:58,338 - INFO -    🎯 High confidence results so far: 0
2025-06-10 23:00:58,338 - INFO -    🏆 HIGH CONFIDENCE ACHIEVED! (0.962)
2025-06-10 23:00:58,339 - INFO - 🎉 ENHANCED OCR PROCESSING COMPLETED!
2025-06-10 23:00:58,339 - INFO - ⏱️  Total time: 12.25 seconds
2025-06-10 23:00:58,339 - INFO - ⚡ Processing speed: 0.1 configurations/second
2025-06-10 23:00:58,339 - INFO - 🎯 High confidence matches (≥95%): 0
2025-06-10 23:00:58,339 - INFO - ⭐ Excellent matches (≥90%): 2
2025-06-10 23:00:58,339 - INFO - 👍 Good matches (≥80%): 2
2025-06-10 23:00:58,339 - INFO - 🏆 Best similarity achieved: 0.000
2025-06-10 23:00:58,339 - INFO - ⚡ Time saved by early termination: 0.0 seconds
2025-06-10 23:01:28,386 - INFO - 💻 GPU acceleration not available, using CPU only
2025-06-10 23:01:28,386 - INFO - Reference lines:
2025-06-10 23:01:28,386 - INFO -   1: -----BEGIN RSA PRIVATE KEY-----
2025-06-10 23:01:28,386 - INFO -   2: MIIJKQIBAAKCAgEApXLc+tmBmDZl6NxuM+IohxYTOph3G/Pt/plQH3KsowSzM62E
2025-06-10 23:01:28,386 - INFO -   3: TSrkn9n3+qjLYU3ThgPPWCBBKdlf57c27eja37YWq7tlro6XD97YO4PtXMbiB6vn
2025-06-10 23:01:28,386 - INFO -   4: amPr13GFnuxQ1FtMDjGp91q234B2p7/QD4AJ/oz1TzAwyibAe80qWiuMqH3R6Kr0
2025-06-10 23:01:28,393 - INFO - 📋 Generated 5790 comprehensive OCR configurations
2025-06-10 23:01:28,393 - INFO - 🎯 Priority configurations: 10
2025-06-10 23:01:28,394 - INFO - 🔍 Standard configurations: 5600
2025-06-10 23:01:28,394 - INFO - 🧪 Extended configurations: 180
2025-06-10 23:01:28,394 - INFO - 🚀 Enhanced OCR Validator initialized
2025-06-10 23:01:28,394 - INFO - 💻 Using 32 CPU cores for maximum performance
2025-06-10 23:01:28,394 - INFO - 🎯 High confidence threshold: 95.0%
2025-06-10 23:01:28,394 - INFO - 📊 Total configurations to test: 5790
2025-06-10 23:01:28,394 - INFO - 🖥️  GPU acceleration: Not available
2025-06-10 23:01:28,394 - INFO - 📝 Reference lines loaded: 4
2025-06-10 23:01:28,394 - INFO - Found 1 image files
2025-06-10 23:01:28,394 - INFO -   - WhatsApp Image 2025-06-06 at 09.57.47.jpeg
2025-06-10 23:01:28,394 - INFO - 🚀 Starting ENHANCED multithreaded OCR processing
2025-06-10 23:01:28,394 - INFO - 💻 Using ALL 32 CPU cores for maximum performance
2025-06-10 23:01:28,394 - INFO - 🖼️  Processing 1 images
2025-06-10 23:01:28,394 - INFO - ⚙️  Testing up to 5790 configurations per image
2025-06-10 23:01:28,394 - INFO - 🎯 High confidence threshold: 95.0%
2025-06-10 23:01:28,394 - INFO - 🔬 Ground truth accuracy assumption: 98.0%
2025-06-10 23:01:28,394 - INFO - 🖼️  Processing image: WhatsApp Image 2025-06-06 at 09.57.47.jpeg
2025-06-10 23:03:28,666 - INFO - 🛑 Received interrupt signal, finishing current tasks...
2025-06-10 23:03:28,672 - ERROR - Error extracting text with Tesseract: (-2, '')
2025-06-10 23:03:28,674 - INFO - ✅ Completed WhatsApp Image 2025-06-06 at 09.57.47.jpeg: 24 results, 0 high confidence
2025-06-10 23:03:28,674 - INFO - ✅ Completed 1/1: WhatsApp Image 2025-06-06 at 09.57.47.jpeg
2025-06-10 23:03:28,674 - INFO -    📊 Best first-4-lines similarity: 0.969
2025-06-10 23:03:28,674 - INFO -    ⚡ Processing rate: 0.0 configs/sec
2025-06-10 23:03:28,674 - INFO -    🎯 High confidence results so far: 0
2025-06-10 23:03:28,674 - INFO -    🏆 HIGH CONFIDENCE ACHIEVED! (0.969)
2025-06-10 23:03:28,674 - INFO - 🎉 ENHANCED OCR PROCESSING COMPLETED!
2025-06-10 23:03:28,674 - INFO - ⏱️  Total time: 120.28 seconds
2025-06-10 23:03:28,674 - INFO - ⚡ Processing speed: 0.0 configurations/second
2025-06-10 23:03:28,674 - INFO - 🎯 High confidence matches (≥95%): 0
2025-06-10 23:03:28,674 - INFO - ⭐ Excellent matches (≥90%): 8
2025-06-10 23:03:28,674 - INFO - 👍 Good matches (≥80%): 8
2025-06-10 23:03:28,674 - INFO - 🏆 Best similarity achieved: 0.000
2025-06-10 23:03:28,674 - INFO - ⚡ Time saved by early termination: 138707.3 seconds
2025-06-10 23:03:28,675 - INFO - 📄 Enhanced results saved to enhanced_ocr_validation_20250610_230328.json and enhanced_ocr_validation_20250610_230328.txt
2025-06-10 23:08:42,250 - INFO - 💻 GPU acceleration not available, using CPU only
2025-06-10 23:08:42,251 - INFO - Reference lines:
2025-06-10 23:08:42,251 - INFO -   1: -----BEGIN RSA PRIVATE KEY-----
2025-06-10 23:08:42,251 - INFO -   2: MIIJKQIBAAKCAgEApXLc+tmBmDZl6NxuM+IohxYTOph3G/Pt/plQH3KsowSzM62E
2025-06-10 23:08:42,251 - INFO -   3: TSrkn9n3+qjLYU3ThgPPWCBBKdlf57c27eja37YWq7tlro6XD97YO4PtXMbiB6vn
2025-06-10 23:08:42,251 - INFO -   4: amPr13GFnuxQ1FtMDjGp91q234B2p7/QD4AJ/oz1TzAwyibAe80qWiuMqH3R6Kr0
2025-06-10 23:08:42,257 - INFO - 📋 Generated 5790 comprehensive OCR configurations
2025-06-10 23:08:42,257 - INFO - 🎯 Priority configurations: 10
2025-06-10 23:08:42,257 - INFO - 🔍 Standard configurations: 5600
2025-06-10 23:08:42,257 - INFO - 🧪 Extended configurations: 180
2025-06-10 23:08:42,257 - INFO - 🚀 Enhanced OCR Validator initialized with ThreadPoolExecutor
2025-06-10 23:08:42,257 - INFO - 💻 Using 4 threads for maximum performance
2025-06-10 23:08:42,257 - INFO - 🎯 High confidence threshold: 95.0%
2025-06-10 23:08:42,257 - INFO - 📊 Total configurations to test: 5790
2025-06-10 23:08:42,257 - INFO - 🖥️  GPU acceleration: Not available
2025-06-10 23:08:42,257 - INFO - 📝 Reference lines loaded: 4
2025-06-10 23:08:42,258 - INFO - Found 1 image files
2025-06-10 23:08:42,258 - INFO -   - WhatsApp Image 2025-06-06 at 09.57.47.jpeg
2025-06-10 23:08:42,258 - INFO - 🚀 Starting ENHANCED multithreaded OCR processing
2025-06-10 23:08:42,258 - INFO - 💻 Using ThreadPoolExecutor with 4 threads
2025-06-10 23:08:42,258 - INFO - 🖼️  Processing 1 images
2025-06-10 23:08:42,258 - INFO - ⚙️  Testing up to 2 configurations per image
2025-06-10 23:08:42,258 - INFO - 📊 Total configurations: 2
2025-06-10 23:08:42,258 - INFO - 🎯 High confidence threshold: 95.0%
2025-06-10 23:08:42,258 - INFO - 🔬 Ground truth accuracy assumption: 98.0%
2025-06-10 23:08:42,258 - INFO - 🎮 GPU acceleration: Disabled
2025-06-10 23:08:42,258 - INFO - 🖼️  Processing image: WhatsApp Image 2025-06-06 at 09.57.47.jpeg
2025-06-10 23:08:44,563 - INFO - ✅ Completed WhatsApp Image 2025-06-06 at 09.57.47.jpeg: 2 results, 0 high confidence
2025-06-10 23:08:44,563 - INFO - ✅ Completed 1/1: WhatsApp Image 2025-06-06 at 09.57.47.jpeg
2025-06-10 23:08:44,563 - INFO -    📊 Best first-4-lines similarity: 0.969
2025-06-10 23:08:44,563 - INFO -    ⚡ Processing rate: 0.4 configs/sec
2025-06-10 23:08:44,563 - INFO -    🧵 Active workers: 0/4
2025-06-10 23:08:44,563 - INFO -    🎯 High confidence results so far: 0
2025-06-10 23:08:44,563 - INFO -    💾 Memory usage: 16.8%
2025-06-10 23:08:44,563 - INFO -    🏆 HIGH CONFIDENCE ACHIEVED! (0.969)
2025-06-10 23:08:44,563 - INFO - 🎉 ENHANCED OCR PROCESSING COMPLETED!
2025-06-10 23:08:44,563 - INFO - ⏱️  Total time: 2.31 seconds
2025-06-10 23:08:44,563 - INFO - ⚡ Processing speed: 0.4 configurations/second
2025-06-10 23:08:44,563 - INFO - 🎯 High confidence matches (≥95%): 0
2025-06-10 23:08:44,563 - INFO - ⭐ Excellent matches (≥90%): 1
2025-06-10 23:08:44,563 - INFO - 👍 Good matches (≥80%): 1
2025-06-10 23:08:44,563 - INFO - 🏆 Best similarity achieved: 0.000
2025-06-10 23:08:44,563 - INFO - ⚡ Time saved by early termination: 0.0 seconds
2025-06-10 23:08:44,580 - INFO - 💻 GPU acceleration not available, using CPU only
2025-06-10 23:08:44,580 - INFO - Reference lines:
2025-06-10 23:08:44,580 - INFO -   1: -----BEGIN RSA PRIVATE KEY-----
2025-06-10 23:08:44,580 - INFO -   2: MIIJKQIBAAKCAgEApXLc+tmBmDZl6NxuM+IohxYTOph3G/Pt/plQH3KsowSzM62E
2025-06-10 23:08:44,580 - INFO -   3: TSrkn9n3+qjLYU3ThgPPWCBBKdlf57c27eja37YWq7tlro6XD97YO4PtXMbiB6vn
2025-06-10 23:08:44,580 - INFO -   4: amPr13GFnuxQ1FtMDjGp91q234B2p7/QD4AJ/oz1TzAwyibAe80qWiuMqH3R6Kr0
2025-06-10 23:08:44,585 - INFO - 📋 Generated 5790 comprehensive OCR configurations
2025-06-10 23:08:44,586 - INFO - 🎯 Priority configurations: 10
2025-06-10 23:08:44,586 - INFO - 🔍 Standard configurations: 5600
2025-06-10 23:08:44,586 - INFO - 🧪 Extended configurations: 180
2025-06-10 23:08:44,586 - INFO - 🚀 Enhanced OCR Validator initialized with ProcessPoolExecutor
2025-06-10 23:08:44,586 - INFO - 💻 Using 4 processes for maximum performance
2025-06-10 23:08:44,586 - INFO - 🎯 High confidence threshold: 95.0%
2025-06-10 23:08:44,586 - INFO - 📊 Total configurations to test: 5790
2025-06-10 23:08:44,586 - INFO - 🖥️  GPU acceleration: Not available
2025-06-10 23:08:44,586 - INFO - 📝 Reference lines loaded: 4
2025-06-10 23:08:44,587 - INFO - Found 1 image files
2025-06-10 23:08:44,587 - INFO -   - WhatsApp Image 2025-06-06 at 09.57.47.jpeg
2025-06-10 23:08:44,587 - INFO - 🚀 Starting ENHANCED multithreaded OCR processing
2025-06-10 23:08:44,587 - INFO - 💻 Using ProcessPoolExecutor with 4 processes
2025-06-10 23:08:44,587 - INFO - 🖼️  Processing 1 images
2025-06-10 23:08:44,587 - INFO - ⚙️  Testing up to 2 configurations per image
2025-06-10 23:08:44,587 - INFO - 📊 Total configurations: 2
2025-06-10 23:08:44,587 - INFO - 🎯 High confidence threshold: 95.0%
2025-06-10 23:08:44,587 - INFO - 🔬 Ground truth accuracy assumption: 98.0%
2025-06-10 23:08:44,587 - INFO - 🎮 GPU acceleration: Disabled
2025-06-10 23:08:44,598 - ERROR - ❌ Error processing WhatsApp Image 2025-06-06 at 09.57.47.jpeg: cannot pickle '_thread.lock' object
2025-06-10 23:08:44,600 - INFO - 🎉 ENHANCED OCR PROCESSING COMPLETED!
2025-06-10 23:08:44,600 - INFO - ⏱️  Total time: 0.01 seconds
2025-06-10 23:08:44,600 - INFO - ⚡ Processing speed: 0.0 configurations/second
2025-06-10 23:08:44,600 - INFO - 🎯 High confidence matches (≥95%): 0
2025-06-10 23:08:44,600 - INFO - ⭐ Excellent matches (≥90%): 0
2025-06-10 23:08:44,600 - INFO - 👍 Good matches (≥80%): 0
2025-06-10 23:08:44,600 - INFO - 🏆 Best similarity achieved: 0.000
2025-06-10 23:08:44,600 - INFO - ⚡ Time saved by early termination: 0.0 seconds
2025-06-10 23:08:44,617 - INFO - 💻 GPU acceleration not available, using CPU only
2025-06-10 23:08:44,617 - INFO - Reference lines:
2025-06-10 23:08:44,617 - INFO -   1: -----BEGIN RSA PRIVATE KEY-----
2025-06-10 23:08:44,617 - INFO -   2: MIIJKQIBAAKCAgEApXLc+tmBmDZl6NxuM+IohxYTOph3G/Pt/plQH3KsowSzM62E
2025-06-10 23:08:44,617 - INFO -   3: TSrkn9n3+qjLYU3ThgPPWCBBKdlf57c27eja37YWq7tlro6XD97YO4PtXMbiB6vn
2025-06-10 23:08:44,617 - INFO -   4: amPr13GFnuxQ1FtMDjGp91q234B2p7/QD4AJ/oz1TzAwyibAe80qWiuMqH3R6Kr0
2025-06-10 23:08:44,623 - INFO - 📋 Generated 5790 comprehensive OCR configurations
2025-06-10 23:08:44,623 - INFO - 🎯 Priority configurations: 10
2025-06-10 23:08:44,624 - INFO - 🔍 Standard configurations: 5600
2025-06-10 23:08:44,624 - INFO - 🧪 Extended configurations: 180
2025-06-10 23:08:44,624 - INFO - 🚀 Enhanced OCR Validator initialized with ThreadPoolExecutor
2025-06-10 23:08:44,624 - INFO - 💻 Using 6 threads for maximum performance
2025-06-10 23:08:44,624 - INFO - 🎯 High confidence threshold: 95.0%
2025-06-10 23:08:44,624 - INFO - 📊 Total configurations to test: 5790
2025-06-10 23:08:44,624 - INFO - 🖥️  GPU acceleration: Not available
2025-06-10 23:08:44,624 - INFO - 📝 Reference lines loaded: 4
2025-06-10 23:08:44,625 - INFO - Found 1 image files
2025-06-10 23:08:44,625 - INFO -   - WhatsApp Image 2025-06-06 at 09.57.47.jpeg
2025-06-10 23:08:44,625 - INFO - 🚀 Starting ENHANCED multithreaded OCR processing
2025-06-10 23:08:44,625 - INFO - 💻 Using ThreadPoolExecutor with 6 threads
2025-06-10 23:08:44,625 - INFO - 🖼️  Processing 1 images
2025-06-10 23:08:44,625 - INFO - ⚙️  Testing up to 36 configurations per image
2025-06-10 23:08:44,625 - INFO - 📊 Total configurations: 36
2025-06-10 23:08:44,625 - INFO - 🎯 High confidence threshold: 95.0%
2025-06-10 23:08:44,625 - INFO - 🔬 Ground truth accuracy assumption: 98.0%
2025-06-10 23:08:44,625 - INFO - 🎮 GPU acceleration: Disabled
2025-06-10 23:08:44,625 - INFO - 🖼️  Processing image: WhatsApp Image 2025-06-06 at 09.57.47.jpeg
2025-06-10 23:10:27,894 - INFO - ✅ Completed WhatsApp Image 2025-06-06 at 09.57.47.jpeg: 36 results, 0 high confidence
2025-06-10 23:10:27,894 - INFO - ✅ Completed 1/1: WhatsApp Image 2025-06-06 at 09.57.47.jpeg
2025-06-10 23:10:27,895 - INFO -    📊 Best first-4-lines similarity: 0.973
2025-06-10 23:10:27,895 - INFO -    ⚡ Processing rate: 0.1 configs/sec
2025-06-10 23:10:27,895 - INFO -    🧵 Active workers: 0/6
2025-06-10 23:10:27,895 - INFO -    🎯 High confidence results so far: 0
2025-06-10 23:10:27,895 - INFO -    💾 Memory usage: 17.0%
2025-06-10 23:10:27,895 - INFO -    🏆 HIGH CONFIDENCE ACHIEVED! (0.973)
2025-06-10 23:10:27,895 - INFO - 🎉 ENHANCED OCR PROCESSING COMPLETED!
2025-06-10 23:10:27,895 - INFO - ⏱️  Total time: 103.27 seconds
2025-06-10 23:10:27,895 - INFO - ⚡ Processing speed: 0.1 configurations/second
2025-06-10 23:10:27,895 - INFO - 🎯 High confidence matches (≥95%): 0
2025-06-10 23:10:27,895 - INFO - ⭐ Excellent matches (≥90%): 12
2025-06-10 23:10:27,895 - INFO - 👍 Good matches (≥80%): 12
2025-06-10 23:10:27,895 - INFO - 🏆 Best similarity achieved: 0.000
2025-06-10 23:10:27,895 - INFO - ⚡ Time saved by early termination: 0.0 seconds
2025-06-10 23:10:27,910 - INFO - 💻 GPU acceleration not available, using CPU only
2025-06-10 23:10:27,911 - INFO - Reference lines:
2025-06-10 23:10:27,911 - INFO -   1: -----BEGIN RSA PRIVATE KEY-----
2025-06-10 23:10:27,911 - INFO -   2: MIIJKQIBAAKCAgEApXLc+tmBmDZl6NxuM+IohxYTOph3G/Pt/plQH3KsowSzM62E
2025-06-10 23:10:27,911 - INFO -   3: TSrkn9n3+qjLYU3ThgPPWCBBKdlf57c27eja37YWq7tlro6XD97YO4PtXMbiB6vn
2025-06-10 23:10:27,911 - INFO -   4: amPr13GFnuxQ1FtMDjGp91q234B2p7/QD4AJ/oz1TzAwyibAe80qWiuMqH3R6Kr0
2025-06-10 23:10:27,918 - INFO - 📋 Generated 5790 comprehensive OCR configurations
2025-06-10 23:10:27,918 - INFO - 🎯 Priority configurations: 10
2025-06-10 23:10:27,918 - INFO - 🔍 Standard configurations: 5600
2025-06-10 23:10:27,918 - INFO - 🧪 Extended configurations: 180
2025-06-10 23:10:27,918 - INFO - 🚀 Enhanced OCR Validator initialized with ThreadPoolExecutor
2025-06-10 23:10:27,918 - INFO - 💻 Using 4 threads for maximum performance
2025-06-10 23:10:27,918 - INFO - 🎯 High confidence threshold: 95.0%
2025-06-10 23:10:27,918 - INFO - 📊 Total configurations to test: 5790
2025-06-10 23:10:27,918 - INFO - 🖥️  GPU acceleration: Not available
2025-06-10 23:10:27,918 - INFO - 📝 Reference lines loaded: 4
2025-06-10 23:10:27,919 - INFO - Found 1 image files
2025-06-10 23:10:27,919 - INFO -   - WhatsApp Image 2025-06-06 at 09.57.47.jpeg
2025-06-10 23:10:27,919 - INFO - 🚀 Starting ENHANCED multithreaded OCR processing
2025-06-10 23:10:27,919 - INFO - 💻 Using ThreadPoolExecutor with 4 threads
2025-06-10 23:10:27,919 - INFO - 🖼️  Processing 1 images
2025-06-10 23:10:27,919 - INFO - ⚙️  Testing up to 2 configurations per image
2025-06-10 23:10:27,919 - INFO - 📊 Total configurations: 2
2025-06-10 23:10:27,919 - INFO - 🎯 High confidence threshold: 95.0%
2025-06-10 23:10:27,919 - INFO - 🔬 Ground truth accuracy assumption: 98.0%
2025-06-10 23:10:27,919 - INFO - 🎮 GPU acceleration: Disabled
2025-06-10 23:10:27,919 - INFO - 🖼️  Processing image: WhatsApp Image 2025-06-06 at 09.57.47.jpeg
2025-06-10 23:10:30,862 - INFO - ✅ Completed WhatsApp Image 2025-06-06 at 09.57.47.jpeg: 2 results, 0 high confidence
2025-06-10 23:10:30,862 - INFO - ✅ Completed 1/1: WhatsApp Image 2025-06-06 at 09.57.47.jpeg
2025-06-10 23:10:30,862 - INFO -    📊 Best first-4-lines similarity: 0.966
2025-06-10 23:10:30,862 - INFO -    ⚡ Processing rate: 0.3 configs/sec
2025-06-10 23:10:30,862 - INFO -    🧵 Active workers: 0/4
2025-06-10 23:10:30,862 - INFO -    🎯 High confidence results so far: 0
2025-06-10 23:10:30,862 - INFO -    💾 Memory usage: 17.0%
2025-06-10 23:10:30,862 - INFO -    🏆 HIGH CONFIDENCE ACHIEVED! (0.966)
2025-06-10 23:10:30,863 - INFO - 🎉 ENHANCED OCR PROCESSING COMPLETED!
2025-06-10 23:10:30,863 - INFO - ⏱️  Total time: 2.94 seconds
2025-06-10 23:10:30,863 - INFO - ⚡ Processing speed: 0.3 configurations/second
2025-06-10 23:10:30,863 - INFO - 🎯 High confidence matches (≥95%): 0
2025-06-10 23:10:30,863 - INFO - ⭐ Excellent matches (≥90%): 1
2025-06-10 23:10:30,863 - INFO - 👍 Good matches (≥80%): 1
2025-06-10 23:10:30,863 - INFO - 🏆 Best similarity achieved: 0.000
2025-06-10 23:10:30,863 - INFO - ⚡ Time saved by early termination: 0.0 seconds
