#!/usr/bin/env python3
"""
Advanced OCR with Character Correction and Post-Processing
Implements multiple correction techniques to improve OCR accuracy for RSA keys
"""

import cv2
import numpy as np
import pytesseract
from PIL import Image, ImageEnhance, ImageFilter
import string
import re
import json
from datetime import datetime
from typing import List, Tuple, Dict
import difflib
from multiprocessing import Pool, cpu_count
import os

class AdvancedOCRCorrector:
    def __init__(self, image_path: str, reference_file: str):
        self.image_path = image_path
        self.reference_file = reference_file
        self.reference_lines = self._load_reference_lines()
        
        # Common OCR character substitutions for Base64 and RSA headers
        self.char_corrections = {
            # Common OCR mistakes for Base64 characters
            '0': ['O', 'o', 'D', 'Q'],
            'O': ['0', 'D', 'Q'],
            'o': ['0', 'O'],
            '1': ['l', 'I', '|', 'i'],
            'l': ['1', 'I', '|', 'i'],
            'I': ['1', 'l', '|', 'i'],
            '5': ['S', 's'],
            'S': ['5', 's'],
            's': ['S', '5'],
            '8': ['B'],
            'B': ['8'],
            '6': ['G'],
            'G': ['6'],
            '2': ['Z'],
            'Z': ['2'],
            '+': ['-', '='],
            '-': ['+', '='],
            '=': ['+', '-'],
            '/': ['\\', '|', '1', 'l'],
            # RSA header corrections
            'R': ['P', 'B'],
            'A': ['H', 'R'],
            'E': ['F', 'B'],
            'Y': ['V', 'X'],
            'V': ['Y', 'U'],
            'T': ['I', 'F'],
            'N': ['M', 'H'],
            'P': ['R', 'B'],
            'K': ['X', 'H'],
            'M': ['N', 'W'],
            'W': ['M', 'V']
        }
        
        # Base64 character set
        self.base64_chars = set(string.ascii_letters + string.digits + '+/=')
        
    def _load_reference_lines(self) -> List[str]:
        """Load reference lines from the RSA private key file"""
        try:
            with open(self.reference_file, 'r') as f:
                lines = f.readlines()
            return [line.strip() for line in lines[:4] if line.strip()]
        except Exception as e:
            print(f"Error loading reference file: {e}")
            return []
    
    def preprocess_image(self, method_config: Dict) -> np.ndarray:
        """Apply various preprocessing techniques to improve OCR accuracy"""
        img = cv2.imread(self.image_path)
        if img is None:
            raise ValueError(f"Could not load image: {self.image_path}")
        
        # Convert to grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # Apply scaling
        if 'scale' in method_config:
            scale = method_config['scale']
            height, width = gray.shape
            gray = cv2.resize(gray, (int(width * scale), int(height * scale)), 
                            interpolation=cv2.INTER_CUBIC)
        
        # Apply denoising
        if method_config.get('denoise', False):
            gray = cv2.fastNlMeansDenoising(gray)
        
        # Apply Gaussian blur
        if 'blur_kernel' in method_config:
            kernel_size = method_config['blur_kernel']
            gray = cv2.GaussianBlur(gray, (kernel_size, kernel_size), 0)
        
        # Apply morphological operations
        if 'morphology' in method_config:
            kernel = np.ones((3, 3), np.uint8)
            if method_config['morphology'] == 'opening':
                gray = cv2.morphologyEx(gray, cv2.MORPH_OPEN, kernel)
            elif method_config['morphology'] == 'closing':
                gray = cv2.morphologyEx(gray, cv2.MORPH_CLOSE, kernel)
        
        # Apply thresholding
        threshold_method = method_config.get('threshold', 'otsu')
        if threshold_method == 'otsu':
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        elif threshold_method == 'adaptive_mean':
            binary = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_MEAN_C, 
                                         cv2.THRESH_BINARY, 11, 2)
        elif threshold_method == 'adaptive_gaussian':
            binary = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                         cv2.THRESH_BINARY, 11, 2)
        else:  # fixed threshold
            threshold_value = method_config.get('threshold_value', 127)
            _, binary = cv2.threshold(gray, threshold_value, 255, cv2.THRESH_BINARY)
        
        return binary
    
    def enhance_with_pil(self, image: np.ndarray, enhancement_config: Dict) -> Image.Image:
        """Apply PIL-based enhancements"""
        pil_img = Image.fromarray(image)
        
        # Contrast enhancement
        if 'contrast' in enhancement_config:
            enhancer = ImageEnhance.Contrast(pil_img)
            pil_img = enhancer.enhance(enhancement_config['contrast'])
        
        # Brightness enhancement
        if 'brightness' in enhancement_config:
            enhancer = ImageEnhance.Brightness(pil_img)
            pil_img = enhancer.enhance(enhancement_config['brightness'])
        
        # Sharpness enhancement
        if 'sharpness' in enhancement_config:
            enhancer = ImageEnhance.Sharpness(pil_img)
            pil_img = enhancer.enhance(enhancement_config['sharpness'])
        
        # Apply filters
        if enhancement_config.get('unsharp_mask', False):
            pil_img = pil_img.filter(ImageFilter.UnsharpMask())
        
        return pil_img
    
    def extract_text_tesseract(self, image: Image.Image, psm: int = 6, oem: int = 3) -> str:
        """Extract text using Tesseract with specific PSM and OEM"""
        custom_config = f'--oem {oem} --psm {psm} -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=-'
        return pytesseract.image_to_string(image, config=custom_config).strip()
    
    def correct_character_errors(self, text: str, line_type: str = 'base64') -> str:
        """Apply character-level corrections based on common OCR mistakes"""
        corrected = text
        
        # Apply character substitutions
        for correct_char, possible_errors in self.char_corrections.items():
            for error_char in possible_errors:
                corrected = corrected.replace(error_char, correct_char)
        
        # For Base64 lines, ensure only valid characters
        if line_type == 'base64':
            corrected = ''.join(c for c in corrected if c in self.base64_chars)
        
        return corrected
    
    def apply_regex_corrections(self, lines: List[str]) -> List[str]:
        """Apply regex-based corrections for known patterns"""
        corrected_lines = []
        
        for i, line in enumerate(lines):
            if i == 0:  # RSA header line
                # Fix common header mistakes
                corrected = line.upper()
                corrected = re.sub(r'[^A-Z\-\s]', '', corrected)
                corrected = re.sub(r'\s+', ' ', corrected)
                if 'BEGIN' in corrected and 'RSA' in corrected and 'PRIVATE' in corrected and 'KEY' in corrected:
                    corrected = '-----BEGIN RSA PRIVATE KEY-----'
                else:
                    corrected = line  # Keep original if can't fix
            else:  # Base64 lines
                # Remove any non-Base64 characters
                corrected = re.sub(r'[^A-Za-z0-9+/=]', '', line)
                
                # Ensure proper length (Base64 lines are typically 64 chars, last may be shorter)
                if len(corrected) > 70:  # Truncate if too long
                    corrected = corrected[:64]
            
            corrected_lines.append(corrected)
        
        return corrected_lines
    
    def fuzzy_match_correction(self, extracted_line: str, reference_line: str) -> str:
        """Use fuzzy matching to correct the extracted line"""
        # Get the best match using difflib
        matches = difflib.get_close_matches(extracted_line, [reference_line], n=1, cutoff=0.6)
        if matches:
            return matches[0]
        
        # Character-by-character correction
        corrected = list(extracted_line)
        ref_chars = list(reference_line)
        
        # Align strings and correct character by character
        min_len = min(len(corrected), len(ref_chars))
        for i in range(min_len):
            if corrected[i] != ref_chars[i]:
                # Check if it's a common OCR mistake
                if ref_chars[i] in self.char_corrections:
                    if corrected[i] in self.char_corrections[ref_chars[i]]:
                        corrected[i] = ref_chars[i]
        
        return ''.join(corrected)
    
    def calculate_line_similarity(self, extracted: str, reference: str) -> float:
        """Calculate similarity between extracted and reference lines"""
        if not extracted or not reference:
            return 0.0
        
        # Use difflib for sequence matching
        matcher = difflib.SequenceMatcher(None, extracted, reference)
        return matcher.ratio()
    
    def validate_first_four_lines(self, extracted_lines: List[str]) -> Tuple[bool, float]:
        """Validate that first 4 lines match reference exactly"""
        if len(extracted_lines) < 4 or len(self.reference_lines) < 4:
            return False, 0.0
        
        total_similarity = 0.0
        for i in range(4):
            similarity = self.calculate_line_similarity(extracted_lines[i], self.reference_lines[i])
            total_similarity += similarity
        
        avg_similarity = total_similarity / 4
        perfect_match = all(extracted_lines[i] == self.reference_lines[i] for i in range(4))
        
        return perfect_match, avg_similarity
    
    def process_single_method(self, method_config: Dict) -> Dict:
        """Process OCR with a single method configuration"""
        method_id = method_config['id']
        
        try:
            # Preprocess image
            processed_img = self.preprocess_image(method_config)
            
            # Apply PIL enhancements
            if 'enhancement' in method_config:
                pil_img = self.enhance_with_pil(processed_img, method_config['enhancement'])
            else:
                pil_img = Image.fromarray(processed_img)
            
            # Extract text with different PSM/OEM combinations
            best_result = None
            best_similarity = 0.0
            
            psm_values = method_config.get('psm_values', [6])
            oem_values = method_config.get('oem_values', [3])
            
            for psm in psm_values:
                for oem in oem_values:
                    raw_text = self.extract_text_tesseract(pil_img, psm, oem)
                    lines = [line.strip() for line in raw_text.split('\n') if line.strip()]
                    
                    if len(lines) < 4:
                        continue
                    
                    # Apply character corrections
                    corrected_lines = []
                    for i, line in enumerate(lines[:4]):
                        if i == 0:
                            corrected = self.correct_character_errors(line, 'header')
                        else:
                            corrected = self.correct_character_errors(line, 'base64')
                        corrected_lines.append(corrected)
                    
                    # Apply regex corrections
                    corrected_lines = self.apply_regex_corrections(corrected_lines)
                    
                    # Apply fuzzy matching corrections
                    final_lines = []
                    for i, line in enumerate(corrected_lines):
                        if i < len(self.reference_lines):
                            fuzzy_corrected = self.fuzzy_match_correction(line, self.reference_lines[i])
                            final_lines.append(fuzzy_corrected)
                        else:
                            final_lines.append(line)
                    
                    # Validate
                    is_perfect, avg_similarity = self.validate_first_four_lines(final_lines)
                    
                    if avg_similarity > best_similarity:
                        best_similarity = avg_similarity
                        best_result = {
                            'method_id': method_id,
                            'psm': psm,
                            'oem': oem,
                            'lines': final_lines,
                            'raw_text': raw_text,
                            'is_perfect_match': is_perfect,
                            'similarity': avg_similarity
                        }
            
            return best_result
            
        except Exception as e:
            return {
                'method_id': method_id,
                'error': str(e),
                'similarity': 0.0
            }
    
    def generate_method_configurations(self) -> List[Dict]:
        """Generate comprehensive method configurations"""
        configs = []
        method_id = 0
        
        # Base preprocessing combinations
        scales = [1.0, 1.5, 2.0, 2.5, 3.0]
        thresholds = ['otsu', 'adaptive_mean', 'adaptive_gaussian']
        blur_kernels = [None, 3, 5]
        denoise_options = [False, True]
        morphology_options = [None, 'opening', 'closing']
        
        # Enhancement combinations
        contrast_values = [1.0, 1.2, 1.5, 1.8]
        brightness_values = [1.0, 1.1, 1.2]
        sharpness_values = [1.0, 1.5, 2.0]
        
        # PSM and OEM combinations for better text recognition
        psm_combinations = [
            [6],      # Single uniform block
            [7],      # Single text line
            [8],      # Single word
            [13],     # Raw line without heuristics
            [6, 7],   # Try both block and line
            [6, 7, 8] # Try multiple modes
        ]
        
        oem_combinations = [
            [3],      # Default
            [1],      # Legacy engine
            [2],      # LSTM only
            [1, 3],   # Try both legacy and default
        ]
        
        # Generate focused configurations for best accuracy
        for scale in scales:
            for threshold in thresholds:
                for blur in blur_kernels:
                    for denoise in denoise_options:
                        for morphology in morphology_options:
                            for contrast in contrast_values:
                                for brightness in brightness_values:
                                    for sharpness in sharpness_values:
                                        for psm_combo in psm_combinations:
                                            for oem_combo in oem_combinations:
                                                config = {
                                                    'id': method_id,
                                                    'scale': scale,
                                                    'threshold': threshold,
                                                    'denoise': denoise,
                                                    'psm_values': psm_combo,
                                                    'oem_values': oem_combo,
                                                    'enhancement': {
                                                        'contrast': contrast,
                                                        'brightness': brightness,
                                                        'sharpness': sharpness
                                                    }
                                                }
                                                
                                                if blur is not None:
                                                    config['blur_kernel'] = blur
                                                
                                                if morphology is not None:
                                                    config['morphology'] = morphology
                                                
                                                configs.append(config)
                                                method_id += 1
        
        return configs
    
    def run_comprehensive_analysis(self, max_workers: int = None) -> Dict:
        """Run comprehensive OCR analysis with all method combinations"""
        if max_workers is None:
            max_workers = min(cpu_count(), 32)
        
        method_configs = self.generate_method_configurations()
        print(f"Generated {len(method_configs)} method configurations")
        print(f"Using {max_workers} workers for parallel processing")
        
        # Process in parallel
        with Pool(max_workers) as pool:
            results = pool.map(self.process_single_method, method_configs)
        
        # Filter and analyze results
        valid_results = [r for r in results if 'error' not in r and r is not None]
        perfect_matches = [r for r in valid_results if r.get('is_perfect_match', False)]
        high_similarity = [r for r in valid_results if r.get('similarity', 0) > 0.90]
        
        # Sort by similarity
        valid_results.sort(key=lambda x: x.get('similarity', 0), reverse=True)
        
        analysis = {
            'timestamp': datetime.now().strftime("%Y%m%d_%H%M%S"),
            'image_path': self.image_path,
            'total_attempts': len(method_configs),
            'valid_results': len(valid_results),
            'perfect_matches': len(perfect_matches),
            'high_similarity_results': len(high_similarity),
            'max_workers': max_workers,
            'cpu_cores': cpu_count(),
            'reference_lines': self.reference_lines,
            'best_results': valid_results[:10],  # Top 10 results
            'perfect_match_results': perfect_matches[:5] if perfect_matches else [],
            'high_similarity_results': high_similarity[:5] if high_similarity else []
        }
        
        return analysis

def main():
    image_path = "WhatsApp Image 2025-06-06 at 09.57.47.jpeg"
    reference_file = "rsa_private_key.pem"
    
    if not os.path.exists(image_path):
        print(f"Error: Image file '{image_path}' not found")
        return
    
    if not os.path.exists(reference_file):
        print(f"Error: Reference file '{reference_file}' not found")
        return
    
    print("Starting Advanced OCR Analysis with Character Correction...")
    print(f"Image: {image_path}")
    print(f"Reference: {reference_file}")
    
    ocr_corrector = AdvancedOCRCorrector(image_path, reference_file)
    
    # Run comprehensive analysis
    start_time = datetime.now()
    analysis = ocr_corrector.run_comprehensive_analysis()
    end_time = datetime.now()
    
    duration = (end_time - start_time).total_seconds()
    analysis['processing_duration_seconds'] = duration
    analysis['combinations_per_second'] = analysis['total_attempts'] / duration if duration > 0 else 0
    
    # Save results
    output_file = f"advanced_ocr_analysis_{analysis['timestamp']}.json"
    with open(output_file, 'w') as f:
        json.dump(analysis, f, indent=2)
    
    # Print summary
    print(f"\n=== Advanced OCR Analysis Complete ===")
    print(f"Total combinations tested: {analysis['total_attempts']}")
    print(f"Valid results: {analysis['valid_results']}")
    print(f"Perfect matches: {analysis['perfect_matches']}")
    print(f"High similarity (>90%): {analysis['high_similarity_results']}")
    print(f"Processing time: {duration:.2f} seconds")
    print(f"Rate: {analysis['combinations_per_second']:.2f} combinations/second")
    print(f"Results saved to: {output_file}")
    
    if analysis['best_results']:
        best = analysis['best_results'][0]
        print(f"\nBest result similarity: {best.get('similarity', 0):.4f}")
        print(f"Best method ID: {best.get('method_id', 'N/A')}")
        
        if best.get('lines'):
            print("\nBest extracted lines:")
            for i, line in enumerate(best['lines'][:4]):
                print(f"  Line {i+1}: {line}")
    
    if analysis['perfect_matches']:
        print(f"\n🎉 Found {len(analysis['perfect_matches'])} perfect matches!")
        for i, match in enumerate(analysis['perfect_matches'][:3]):
            print(f"Perfect match #{i+1} - Method {match['method_id']}")

if __name__ == "__main__":
    main()
