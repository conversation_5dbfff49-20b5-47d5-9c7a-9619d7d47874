# Multithreaded OCR Validator

A Python script that performs Optical Character Recognition (OCR) on images using multithreading and validates the results against reference text from a PEM file.

## Features

- **Multithreaded Processing**: Uses ThreadPoolExecutor to process multiple images concurrently
- **Multiple OCR Configurations**: Tries 25 different combinations of preprocessing and Tesseract settings
- **Reference Validation**: Compares OCR results against the first 4 lines of `rsa_private_key.pem`
- **Comprehensive Reporting**: Generates detailed JSON and text reports
- **Error Handling**: Robust error handling for unsupported formats and OCR failures
- **Performance Metrics**: Shows processing time, similarity scores, and accuracy metrics

## Requirements

- Python 3.6+
- OpenCV (`opencv-python`)
- Tesseract OCR (`pytesseract`)
- Pillow (`Pillow`)
- NumPy (`numpy`)
- Tesseract binary installed on system

## Installation

1. Install Python dependencies:
```bash
pip install -r requirements.txt
```

2. Install Tesseract OCR:
```bash
# Ubuntu/Debian
sudo apt-get install tesseract-ocr

# macOS
brew install tesseract

# Windows
# Download from: https://github.com/UB-Mannheim/tesseract/wiki
```

## Usage

### Basic Usage

```bash
python3 multithreaded_ocr_validator.py
```

This will:
- Process all image files in the current directory
- Use `rsa_private_key.pem` as the reference file
- Try 25 different OCR configurations per image
- Generate detailed reports

### Supported Image Formats

- JPEG (.jpg, .jpeg)
- PNG (.png)
- TIFF (.tiff, .tif)
- BMP (.bmp)
- GIF (.gif)
- WebP (.webp)

### Output Files

The script generates two output files:
- `ocr_validation_YYYYMMDD_HHMMSS.json` - Detailed results in JSON format
- `ocr_validation_YYYYMMDD_HHMMSS.txt` - Human-readable report

## OCR Configurations

The script tries multiple combinations of:

### Preprocessing Methods
- **Original**: No preprocessing
- **Scaled 2x**: 2x scaling with Otsu thresholding
- **Scaled 3x**: 3x scaling with denoising and Otsu thresholding
- **Denoised**: 2x scaling with denoising and adaptive thresholding
- **Enhanced**: 2.5x scaling with denoising, Otsu thresholding, and sharpening

### Tesseract Configurations
- **Default**: PSM 6, OEM 3
- **Single Block**: PSM 6, OEM 1 with character whitelist
- **Single Line**: PSM 7, OEM 3 with character whitelist
- **Raw Line**: PSM 8, OEM 3
- **Sparse Text**: PSM 11, OEM 1 with character whitelist

## Results Interpretation

### Similarity Scores
- **1.000**: Perfect match
- **0.900+**: Excellent match (minor OCR errors)
- **0.800+**: Good match (some OCR errors)
- **0.700+**: Fair match (significant OCR errors)
- **Below 0.700**: Poor match

### Perfect Matches
When the OCR exactly matches the first 4 lines of the reference file.

### High Similarity Matches
Results with similarity > 80% indicate the OCR is working well but may have minor character recognition errors.

## Example Output

```
============================================================
OCR VALIDATION SUMMARY
============================================================
📁 Images processed: 1
🔍 Total OCR attempts: 25
✅ Perfect matches: 0
📊 High similarity (>80%): 10
⏱️  Processing time: 74.53s
🚀 Speed: 0.3 attempts/second

📈 Best similarity results:
   • WhatsApp Image 2025-06-06 at 09.57.47.jpeg: 0.934 (scaled_3x_default)
   • WhatsApp Image 2025-06-06 at 09.57.47.jpeg: 0.925 (scaled_3x_single_block)
   • WhatsApp Image 2025-06-06 at 09.57.47.jpeg: 0.921 (scaled_2x_default)
============================================================
```

## Customization

You can modify the script to:
- Change the reference file path
- Adjust the number of worker threads
- Add new preprocessing methods
- Modify Tesseract configurations
- Change similarity thresholds

## Troubleshooting

1. **No images found**: Ensure image files are in the correct directory
2. **Tesseract not found**: Install Tesseract OCR binary
3. **Poor OCR results**: Try improving image quality (resolution, contrast, lighting)
4. **Memory issues**: Reduce the number of worker threads

## License

This script is provided as-is for educational and testing purposes.
